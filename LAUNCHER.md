# 🚀 PINTEREST AFFILIATE AUTOMATION - LAUNCHER COMMANDS

This file contains all the terminal commands to launch each component of the Pinterest affiliate automation system.

## 📊 SYSTEM OVERVIEW

The system consists of three main components:
1. **Simple Sequential Scraper** - Scrapes Digistore24 products
2. **Enhanced Pinterest Generator** - Generates Pinterest content with AI and Pexels videos
3. **Simple Database Viewer** - Views and manages Pinterest content database

---

## 🔧 PREREQUISITES

### Environment Setup
```bash
# Set Pexels API key (required for video discovery)
export PEXELS_API_KEY="J19IZjpd6OisrbdKMRKstKZ0FwMJs1vZU8V7vusBCsQw3QogFlYxko9v"

# Ensure Ollama is running with Qwen model
ollama serve
ollama pull qwen2.5:0.5b
```

### Required Files
- `digistore24_products.db` - Source database from scraper
- `pinterest_content_enhanced.db` - Pinterest content database (auto-created)

---

## 🕷️ 1. SIMPL<PERSON> SEQUENTIAL SCRAPER

**Purpose**: Scrapes Digistore24 products and affiliate links

### Launch Command
```bash
python3 src/simple_sequential_scraper.py
```

### What It Does
- Scrapes Digistore24 marketplace systematically
- Extracts product titles, categories, and affiliate links
- Handles pagination automatically
- Stores data in `digistore24_products.db`
- Processes products sequentially for reliability

### Output Database
- **File**: `digistore24_products.db`
- **Table**: `products`
- **Fields**: id, title, affiliate_link, category, scraped_at

---

## 🎨 2. ENHANCED PINTEREST GENERATOR

**Purpose**: Generates complete Pinterest content with AI and Pexels videos

### Launch Command
```bash
# With Pexels API key
PEXELS_API_KEY="J19IZjpd6OisrbdKMRKstKZ0FwMJs1vZU8V7vusBCsQw3QogFlYxko9v" python3 src/enhanced_pinterest_generator.py
```

### What It Does
- Reads products from `digistore24_products.db`
- Researches each product using affiliate URLs
- Generates AI-powered Pinterest titles and descriptions
- Finds relevant Pexels videos using AI selection
- Creates hashtags optimized for Pinterest
- Stores complete Pinterest content in `pinterest_content_enhanced.db`

### Features
- **AI Model**: Qwen2.5:0.5b for fast, accurate content generation
- **Web Research**: Crawls actual product pages for accurate information
- **Video Integration**: AI-selected Pexels videos for each product
- **Content Accuracy**: Strict prompts prevent AI hallucination
- **Pinterest Optimization**: Content formatted for Pinterest posting

### Output Database
- **File**: `pinterest_content_enhanced.db`
- **Tables**: `products`, `pinterest_content`
- **Pinterest Fields**: title, description, affiliate_link, video_url, hashtags

---

## 📋 3. SIMPLE DATABASE VIEWER

**Purpose**: Interactive CLI viewer for Pinterest content database

### Launch Command
```bash
python3 simple_db_viewer.py
```

### Interactive Menu Options
```
🔧 OPTIONS:
1. List all products
2. View product details (enter ID)
3. List only unposted products
4. Mark product as posted
5. Mark product as unposted
6. Quit
```

### What It Does
- **Browse Products**: View all Pinterest-ready content
- **Copy-Paste Ready**: Shows formatted content for Pinterest posting
- **Track Status**: Mark products as posted/unposted
- **Filter Views**: Show only pending products
- **Complete Details**: Title, description, affiliate link, video URL

### Usage Example
```bash
Choose (1-6): 2
Enter product ID: 1

# Shows complete Pinterest posting content:
📝 TITLE (copy this):
The Genius Wave: 7-Second Brain Activation

📄 DESCRIPTION (copy this):
[Complete description with hashtags]

🔗 AFFILIATE LINK (copy this):
https://ingeniuswave.com/DSvsl/#aff=AllDayErDay

🎬 PEXELS VIDEO URL:
https://www.pexels.com/video/brain-plus-heart-9162046/
```

---

## 🔄 COMPLETE WORKFLOW

### Step 1: Scrape Products
```bash
python3 src/simple_sequential_scraper.py
```

### Step 2: Generate Pinterest Content
```bash
PEXELS_API_KEY="J19IZjpd6OisrbdKMRKstKZ0FwMJs1vZU8V7vusBCsQw3QogFlYxko9v" python3 src/enhanced_pinterest_generator.py
```

### Step 3: View and Manage Content
```bash
python3 simple_db_viewer.py
```

---

## 🛠️ TROUBLESHOOTING

### Common Issues

**1. Pexels API Key Missing**
```bash
# Error: "PEXELS_API_KEY not found in environment"
# Solution: Set the API key
export PEXELS_API_KEY="J19IZjpd6OisrbdKMRKstKZ0FwMJs1vZU8V7vusBCsQw3QogFlYxko9v"
```

**2. Ollama Not Running**
```bash
# Error: "Ollama not available"
# Solution: Start Ollama service
ollama serve
```

**3. Model Not Found**
```bash
# Error: "Model qwen2.5:0.5b not found"
# Solution: Pull the model
ollama pull qwen2.5:0.5b
```

**4. Database Not Found**
```bash
# Error: "Source database not found"
# Solution: Run scraper first
python3 src/simple_sequential_scraper.py
```

---

## 📊 DATABASE SCHEMAS

### Source Database (digistore24_products.db)
```sql
CREATE TABLE products (
    id INTEGER PRIMARY KEY,
    title TEXT NOT NULL,
    affiliate_link TEXT,
    category TEXT,
    scraped_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Pinterest Database (pinterest_content_enhanced.db)
```sql
CREATE TABLE pinterest_content (
    id INTEGER PRIMARY KEY,
    product_id INTEGER,
    pinterest_title TEXT NOT NULL,
    pinterest_description TEXT NOT NULL,
    affiliate_link TEXT,
    pexels_video_url TEXT,
    video_download_url TEXT,
    is_used BOOLEAN DEFAULT FALSE,
    posted_at TIMESTAMP,
    generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

---

## 🎯 SYSTEM STATUS

- ✅ **Simple Sequential Scraper**: Production ready
- ✅ **Enhanced Pinterest Generator**: Production ready with AI and Pexels integration
- ✅ **Simple Database Viewer**: Production ready with interactive CLI
- ✅ **Content Accuracy**: Fixed AI hallucination issues
- ✅ **Video Integration**: AI-selected Pexels videos for all products
- ✅ **Clean Content**: Removed AI chatter from generated content

**The complete Pinterest affiliate automation system is ready for production use!**
