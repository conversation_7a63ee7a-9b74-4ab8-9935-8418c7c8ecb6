#!/usr/bin/env python3
"""
Clear Pinterest Content Database
Safely clears the Pinterest content database while preserving structure
DOES NOT TOUCH the source scraper database (digistore24_products.db)
"""

import sqlite3
import os
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def clear_pinterest_database():
    """Clear Pinterest content database while preserving structure"""
    
    pinterest_db = "pinterest_content_enhanced.db"
    source_db = "digistore24_products.db"
    
    # Verify source database exists and is not touched
    if not Path(source_db).exists():
        logger.error(f"Source database {source_db} not found!")
        return False
        
    logger.info(f"✅ Source database {source_db} exists and will NOT be touched")
    
    # Check if Pinterest database exists
    if not Path(pinterest_db).exists():
        logger.info(f"Pinterest database {pinterest_db} doesn't exist - will be created fresh")
        return True
        
    try:
        # Connect to Pinterest database
        conn = sqlite3.connect(pinterest_db)
        cursor = conn.cursor()
        
        # Get current record counts
        cursor.execute("SELECT COUNT(*) FROM products")
        products_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM pinterest_content")
        content_count = cursor.fetchone()[0]
        
        logger.info(f"📊 Current Pinterest database status:")
        logger.info(f"   - Products: {products_count}")
        logger.info(f"   - Pinterest content: {content_count}")
        
        # Clear all data from Pinterest tables
        logger.info("🧹 Clearing Pinterest database tables...")
        
        # Clear pinterest_content table
        cursor.execute("DELETE FROM pinterest_content")
        logger.info("   ✅ Cleared pinterest_content table")
        
        # Clear products table (this is copied from source, will be repopulated)
        cursor.execute("DELETE FROM products")
        logger.info("   ✅ Cleared products table")
        
        # Reset auto-increment counters
        cursor.execute("DELETE FROM sqlite_sequence WHERE name='products'")
        cursor.execute("DELETE FROM sqlite_sequence WHERE name='pinterest_content'")
        logger.info("   ✅ Reset auto-increment counters")
        
        # Commit changes
        conn.commit()
        conn.close()
        
        logger.info("🎉 Pinterest database cleared successfully!")
        logger.info(f"📁 Source database {source_db} remains untouched")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error clearing Pinterest database: {e}")
        return False

def verify_source_database():
    """Verify source database has products"""
    source_db = "digistore24_products.db"
    
    try:
        conn = sqlite3.connect(source_db)
        cursor = conn.cursor()
        
        # Get first product to verify
        cursor.execute("SELECT id, title FROM products LIMIT 1")
        first_product = cursor.fetchone()
        
        if first_product:
            logger.info(f"✅ Source database verified - First product: ID {first_product[0]} - {first_product[1][:50]}...")
            
            # Get total count
            cursor.execute("SELECT COUNT(*) FROM products")
            total_count = cursor.fetchone()[0]
            logger.info(f"📊 Total products in source database: {total_count}")
        else:
            logger.warning("⚠️ No products found in source database")
            
        conn.close()
        return first_product is not None
        
    except Exception as e:
        logger.error(f"❌ Error checking source database: {e}")
        return False

if __name__ == "__main__":
    logger.info("🚀 Starting Pinterest database clearing process...")
    
    # Verify source database
    if not verify_source_database():
        logger.error("❌ Source database verification failed")
        exit(1)
    
    # Clear Pinterest database
    if clear_pinterest_database():
        logger.info("✅ Database clearing completed successfully!")
        logger.info("🎯 Ready to generate fresh Pinterest content for product 1")
    else:
        logger.error("❌ Database clearing failed")
        exit(1)
