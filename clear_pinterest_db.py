#!/usr/bin/env python3
"""
Clear Pinterest Content Database for Fresh Generation
Clears pinterest_content_enhanced.db while keeping digistore24_products.db intact
"""

import sqlite3
import os
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def clear_pinterest_database():
    """Clear Pinterest content database while preserving structure"""
    
    pinterest_db = "pinterest_content_enhanced.db"
    source_db = "digistore24_products.db"
    
    # Verify source database exists and is not touched
    if not Path(source_db).exists():
        logger.error(f"Source database {source_db} not found!")
        return False
        
    logger.info(f"✅ Source database {source_db} exists and will NOT be touched")
    
    try:
        # Connect to Pinterest database
        conn = sqlite3.connect(pinterest_db)
        cursor = conn.cursor()
        
        # Get current record counts
        try:
            cursor.execute("SELECT COUNT(*) FROM products")
            products_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM pinterest_content")
            content_count = cursor.fetchone()[0]
            
            logger.info(f"📊 Current Pinterest database:")
            logger.info(f"   - Products: {products_count}")
            logger.info(f"   - Pinterest content: {content_count}")
        except:
            logger.info("📊 Pinterest database exists but may be empty")
        
        # Clear all data from Pinterest tables
        logger.info("🧹 Clearing Pinterest database...")
        
        # Clear pinterest_content table
        cursor.execute("DELETE FROM pinterest_content")
        logger.info("   ✅ Cleared pinterest_content table")
        
        # Clear products table (will be repopulated from source)
        cursor.execute("DELETE FROM products")
        logger.info("   ✅ Cleared products table")
        
        # Reset auto-increment counters
        cursor.execute("DELETE FROM sqlite_sequence WHERE name IN ('products', 'pinterest_content')")
        logger.info("   ✅ Reset auto-increment counters")
        
        # Commit changes
        conn.commit()
        conn.close()
        
        logger.info("🎉 Pinterest database cleared successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error clearing Pinterest database: {e}")
        return False

def check_source_database():
    """Check source database for product 1"""
    source_db = "digistore24_products.db"
    
    try:
        conn = sqlite3.connect(source_db)
        cursor = conn.cursor()
        
        # Get first product
        cursor.execute("SELECT id, title, affiliate_link FROM products ORDER BY id LIMIT 1")
        first_product = cursor.fetchone()
        
        if first_product:
            logger.info(f"✅ Product 1 found: ID {first_product[0]}")
            logger.info(f"   Title: {first_product[1][:60]}...")
            logger.info(f"   Has affiliate link: {'Yes' if first_product[2] else 'No'}")
        else:
            logger.warning("⚠️ No products found in source database")
            
        # Get total count
        cursor.execute("SELECT COUNT(*) FROM products")
        total_count = cursor.fetchone()[0]
        logger.info(f"📊 Total products available: {total_count}")
            
        conn.close()
        return first_product is not None
        
    except Exception as e:
        logger.error(f"❌ Error checking source database: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Pinterest Database Cleaner")
    print("=" * 50)
    
    # Check source database
    logger.info("🔍 Checking source database...")
    if not check_source_database():
        logger.error("❌ Source database check failed")
        exit(1)
    
    # Clear Pinterest database
    logger.info("🧹 Clearing Pinterest database...")
    if clear_pinterest_database():
        print("\n" + "=" * 50)
        print("✅ SUCCESS: Pinterest database cleared!")
        print("🎯 Ready to generate fresh content for product 1")
        print("=" * 50)
    else:
        logger.error("❌ Database clearing failed")
        exit(1)
