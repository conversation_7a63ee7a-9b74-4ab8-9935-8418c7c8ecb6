#!/usr/bin/env python3
"""
Pinterest Database Viewer
Browse and copy content from the database
"""

import sqlite3
import os
from datetime import datetime
from loguru import logger

class DatabaseViewer:
    def __init__(self):
        self.database_path = "pinterest_content_enhanced.db"
    
    def get_all_products(self):
        """Get all products with their content"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT
                    pc.product_id,
                    p.title as product_title,
                    p.description as product_description,
                    p.price,
                    pc.pinterest_title,
                    pc.pinterest_description,
                    pc.affiliate_link,
                    pc.pexels_video_url,
                    pc.is_used,
                    pc.posted_at
                FROM pinterest_content pc
                JOIN products p ON pc.product_id = p.id
                WHERE pc.pinterest_title IS NOT NULL
                ORDER BY p.id ASC
            """)

            products = cursor.fetchall()
            conn.close()

            columns = ['product_id', 'product_title', 'product_description', 'price',
                      'pinterest_title', 'pinterest_description', 'affiliate_link',
                      'pexels_video_url', 'is_used', 'posted_at']
            return [dict(zip(columns, row)) for row in products]
            
        except Exception as e:
            logger.error(f"❌ Failed to get products: {e}")
            return []
    
    def mark_product_posted(self, product_id):
        """Mark a product as posted"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE pinterest_content
                SET is_used = TRUE, posted_at = ?
                WHERE product_id = ?
            """, (datetime.now().isoformat(), product_id))
            
            conn.commit()
            conn.close()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to mark product as posted: {e}")
            return False
    
    def mark_product_unposted(self, product_id):
        """Mark a product as unposted"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE pinterest_content
                SET is_used = FALSE, posted_at = NULL
                WHERE product_id = ?
            """, (product_id,))
            
            conn.commit()
            conn.close()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to mark product as unposted: {e}")
            return False
    
    def display_product_list(self, products):
        """Display a list of all products"""
        print("\n" + "="*100)
        print("📋 ALL PRODUCTS IN DATABASE")
        print("="*100)
        print(f"{'ID':>3} | {'STATUS':^8} | {'TITLE':^50} | {'POSTED DATE':^20}")
        print("-" * 100)
        
        for product in products:
            status = "✅ POSTED" if product['is_used'] else "⏳ PENDING"
            posted_date = product['posted_at'][:10] if product['posted_at'] else "Never"
            title = product['product_title'][:47] + "..." if len(product['product_title']) > 50 else product['product_title']

            print(f"{product['product_id']:>3} | {status:^8} | {title:<50} | {posted_date:^20}")
        
        print("="*100)
    
    def display_product_detail(self, product):
        """Display detailed product information for copy-paste"""
        status = "✅ POSTED" if product['is_used'] else "⏳ PENDING"
        posted_date = product['posted_at'] if product['posted_at'] else "Never"
        
        print("\n" + "="*80)
        print(f"📌 PRODUCT #{product['product_id']}: {product['product_title']}")
        print(f"Status: {status} | Posted: {posted_date}")
        print("="*80)
        
        print("\n💰 PRODUCT INFO:")
        print("-" * 40)
        print(f"Title: {product['product_title']}")
        print(f"Price: {product['price']}")
        print(f"Description: {product['product_description'][:100]}...")
        
        print("\n📝 PINTEREST TITLE (copy this):")
        print("-" * 40)
        print(product['pinterest_title'])
        
        print("\n📄 PINTEREST DESCRIPTION (copy this):")
        print("-" * 40)
        print(product['pinterest_description'])
        
        print("\n🔗 AFFILIATE LINK (copy this):")
        print("-" * 40)
        print(product['affiliate_link'])
        
        if product.get('pexels_video_url'):
            print("\n🎬 PEXELS VIDEO URL:")
            print("-" * 40)
            print(product['pexels_video_url'])
        
        print("\n" + "="*80)

def main():
    """Main database viewer"""
    print("🗄️ Pinterest Database Viewer")
    print("Browse and copy content from your database")
    
    viewer = DatabaseViewer()
    
    while True:
        products = viewer.get_all_products()
        
        if not products:
            print("\n❌ No products found in database!")
            break
        
        unposted_count = len([p for p in products if not p['is_used']])
        posted_count = len(products) - unposted_count
        
        print(f"\n📊 Database Status: {len(products)} total products | {posted_count} posted | {unposted_count} pending")
        
        print("\n🔧 OPTIONS:")
        print("1. View all products (list)")
        print("2. View product details (enter ID)")
        print("3. Mark product as posted")
        print("4. Mark product as unposted")
        print("5. Show only unposted products")
        print("6. Show only posted products")
        print("7. Quit")
        
        choice = input("\nChoose option (1-7): ").strip()
        
        if choice == '1':
            # Show all products list
            viewer.display_product_list(products)
            
        elif choice == '2':
            # Show specific product details
            try:
                product_id = int(input("Enter product ID: "))
                product = next((p for p in products if p['product_id'] == product_id), None)
                if product:
                    viewer.display_product_detail(product)
                    input("\nPress Enter to continue...")
                else:
                    print(f"❌ Product #{product_id} not found")
            except ValueError:
                print("❌ Invalid product ID")
                
        elif choice == '3':
            # Mark as posted
            try:
                product_id = int(input("Enter product ID to mark as posted: "))
                if viewer.mark_product_posted(product_id):
                    print(f"✅ Product #{product_id} marked as posted!")
                else:
                    print(f"❌ Failed to mark product #{product_id}")
            except ValueError:
                print("❌ Invalid product ID")
                
        elif choice == '4':
            # Mark as unposted
            try:
                product_id = int(input("Enter product ID to mark as unposted: "))
                if viewer.mark_product_unposted(product_id):
                    print(f"✅ Product #{product_id} marked as unposted!")
                else:
                    print(f"❌ Failed to mark product #{product_id}")
            except ValueError:
                print("❌ Invalid product ID")
                
        elif choice == '5':
            # Show only unposted
            unposted_products = [p for p in products if not p['is_used']]
            if unposted_products:
                viewer.display_product_list(unposted_products)
            else:
                print("\n✅ No unposted products found!")
                
        elif choice == '6':
            # Show only posted
            posted_products = [p for p in products if p['is_used']]
            if posted_products:
                viewer.display_product_list(posted_products)
            else:
                print("\n📭 No posted products found!")
                
        elif choice == '7':
            break
            
        else:
            print("❌ Invalid choice")
    
    print("\n👋 Database Viewer - Done!")

if __name__ == "__main__":
    main()
