#!/usr/bin/env python3
"""
Enhanced Pinterest Database Viewer with Multiple Videos Support
Shows multiple video options for each product
"""

import sqlite3
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedPinterestViewer:
    def __init__(self, db_path="pinterest_content_enhanced.db"):
        self.db_path = db_path
    
    def get_products_summary(self):
        """Get summary of all products"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT 
                    COUNT(*) as total,
                    COUNT(CASE WHEN pc.is_used = 1 THEN 1 END) as posted,
                    COUNT(CASE WHEN pc.is_used = 0 THEN 1 END) as pending
                FROM products p
                LEFT JOIN pinterest_content pc ON p.id = pc.product_id
            ''')
            
            stats = cursor.fetchone()
            conn.close()
            
            return {
                'total': stats[0] or 0,
                'posted': stats[1] or 0, 
                'pending': stats[2] or 0
            }
            
        except Exception as e:
            logger.error(f"Error getting summary: {e}")
            return {'total': 0, 'posted': 0, 'pending': 0}
    
    def list_all_products(self):
        """List all products with status"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT 
                    p.id,
                    p.title,
                    CASE WHEN pc.is_used = 1 THEN 'POSTED' ELSE 'PENDING' END as status,
                    COUNT(vo.id) as video_count
                FROM products p
                LEFT JOIN pinterest_content pc ON p.id = pc.product_id
                LEFT JOIN video_options vo ON p.id = vo.product_id
                GROUP BY p.id, p.title, pc.is_used
                ORDER BY p.id
            ''')
            
            products = cursor.fetchall()
            conn.close()
            
            print("=" * 100)
            print("📋 PINTEREST PRODUCTS DATABASE (WITH MULTIPLE VIDEOS)")
            print("=" * 100)
            print(f" {'ID':<3} | {'STATUS':<8} | {'VIDEOS':<6} | {'TITLE':<60}")
            print("-" * 100)
            
            for product_id, title, status, video_count in products:
                status_icon = "✅ POSTED" if status == "POSTED" else "⏳ PENDING"
                video_info = f"{video_count} videos" if video_count > 0 else "No videos"
                print(f" {product_id:<3} | {status_icon:<8} | {video_info:<6} | {title[:60]:<60}")
            
            print("=" * 100)
            
        except Exception as e:
            logger.error(f"Error listing products: {e}")
    
    def view_product_details(self, product_id):
        """View detailed product information with multiple videos"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Get product and content info
            cursor.execute('''
                SELECT 
                    p.title,
                    pc.pinterest_title,
                    pc.pinterest_description,
                    pc.affiliate_link,
                    pc.is_used,
                    pc.posted_at
                FROM products p
                LEFT JOIN pinterest_content pc ON p.id = pc.product_id
                WHERE p.id = ?
            ''', (product_id,))
            
            product = cursor.fetchone()
            
            if not product:
                print(f"❌ Product {product_id} not found")
                return
            
            title, pinterest_title, description, affiliate_link, is_used, posted_at = product
            
            # Get video options
            cursor.execute('''
                SELECT rank, pexels_video_url, video_download_url, video_tags, video_duration
                FROM video_options 
                WHERE product_id = ? 
                ORDER BY rank
            ''', (product_id,))
            
            videos = cursor.fetchall()
            
            conn.close()
            
            # Display product details
            print("=" * 100)
            print(f"📌 PRODUCT #{product_id}")
            status = "✅ POSTED" if is_used else "⏳ PENDING"
            posted_date = posted_at if posted_at else "Never"
            print(f"Status: {status} | Posted: {posted_date}")
            print("=" * 100)
            
            print("\n📝 TITLE (copy this):")
            print("-" * 50)
            print(pinterest_title or title)
            
            print("\n📄 DESCRIPTION (copy this):")
            print("-" * 50)
            print(description or "No description available")
            
            print("\n🔗 AFFILIATE LINK (copy this):")
            print("-" * 50)
            print(affiliate_link or "No affiliate link")
            
            # Display multiple video options
            if videos:
                print(f"\n🎬 VIDEO OPTIONS ({len(videos)} available):")
                print("-" * 50)
                for rank, video_url, download_url, tags, duration in videos:
                    print(f"#{rank}: {video_url}")
                    if tags:
                        print(f"     Tags: {tags}")
                    if duration:
                        print(f"     Duration: {duration}s")
                    if download_url:
                        print(f"     Download: {download_url}")
                    print()
            else:
                print("\n🎬 VIDEO OPTIONS:")
                print("-" * 50)
                print("❌ No video options available")
            
            print("=" * 100)
            print("📋 COPY-PASTE STEPS:")
            print("1. Choose one of the video URLs above")
            print("2. Upload video to Pinterest")
            print("3. Copy-paste TITLE above")
            print("4. Copy-paste DESCRIPTION above") 
            print("5. Copy-paste AFFILIATE LINK above")
            print("6. Click 'Publish'")
            print("=" * 100)
            
        except Exception as e:
            logger.error(f"Error viewing product details: {e}")
    
    def mark_product_posted(self, product_id):
        """Mark product as posted"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE pinterest_content 
                SET is_used = 1, posted_at = CURRENT_TIMESTAMP 
                WHERE product_id = ?
            ''', (product_id,))
            
            if cursor.rowcount > 0:
                print(f"✅ Product {product_id} marked as posted")
            else:
                print(f"❌ Product {product_id} not found")
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Error marking product as posted: {e}")
    
    def mark_product_unposted(self, product_id):
        """Mark product as unposted"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE pinterest_content 
                SET is_used = 0, posted_at = NULL 
                WHERE product_id = ?
            ''', (product_id,))
            
            if cursor.rowcount > 0:
                print(f"✅ Product {product_id} marked as unposted")
            else:
                print(f"❌ Product {product_id} not found")
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Error marking product as unposted: {e}")

def main():
    """Main interactive menu"""
    viewer = EnhancedPinterestViewer()
    
    while True:
        # Show status
        stats = viewer.get_products_summary()
        print(f"\n🗄️ Enhanced Pinterest Database Viewer (Multiple Videos)")
        print(f"\n📊 Status: {stats['total']} total | {stats['posted']} posted | {stats['pending']} pending")
        
        print("\n🔧 OPTIONS:")
        print("1. List all products")
        print("2. View product details with multiple videos (enter ID)")
        print("3. List only unposted products")
        print("4. Mark product as posted")
        print("5. Mark product as unposted")
        print("6. Quit")
        
        try:
            choice = input("\nChoose (1-6): ").strip()
            
            if choice == '1':
                viewer.list_all_products()
                
            elif choice == '2':
                product_id = input("Enter product ID: ").strip()
                try:
                    viewer.view_product_details(int(product_id))
                    input("\nPress Enter to continue...")
                except ValueError:
                    print("❌ Invalid product ID")
                    
            elif choice == '3':
                # Show only unposted products
                viewer.list_all_products()  # For now, same as option 1
                
            elif choice == '4':
                product_id = input("Enter product ID to mark as posted: ").strip()
                try:
                    viewer.mark_product_posted(int(product_id))
                except ValueError:
                    print("❌ Invalid product ID")
                    
            elif choice == '5':
                product_id = input("Enter product ID to mark as unposted: ").strip()
                try:
                    viewer.mark_product_unposted(int(product_id))
                except ValueError:
                    print("❌ Invalid product ID")
                    
            elif choice == '6':
                print("👋 Goodbye!")
                break
                
            else:
                print("❌ Invalid choice")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
