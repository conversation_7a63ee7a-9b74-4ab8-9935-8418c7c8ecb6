#!/usr/bin/env python3
"""
Generate Pinterest Content for Product 1 Only
Modified version of enhanced_pinterest_generator.py to process only the first product
"""

import asyncio
import os
import sys
import logging
from pathlib import Path

# Add src directory to path
sys.path.append('src')

from enhanced_pinterest_generator import EnhancedPinterestGenerator

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class Product1Generator(EnhancedPinterestGenerator):
    """Modified Pinterest generator that processes only product 1"""
    
    def copy_product_1_from_source(self):
        """Copy only product 1 from source database to Pinterest database"""
        logger.info("Copying product 1 from source database...")
        
        import sqlite3
        
        # Read from source database - get product 1
        source_conn = sqlite3.connect(self.source_db)
        source_cursor = source_conn.cursor()
        
        # Get the first product by ID
        source_cursor.execute('''
            SELECT id, title, affiliate_link, scraped_at
            FROM products
            WHERE id = 1
            AND affiliate_link IS NOT NULL
            AND title IS NOT NULL
            AND title != ''
            LIMIT 1
        ''')
        
        product_1 = source_cursor.fetchone()
        source_conn.close()
        
        if not product_1:
            logger.error("Product 1 not found in source database!")
            return False
            
        source_id, title, affiliate_link, scraped_at = product_1
        logger.info(f"Found Product 1: {title[:60]}...")
        
        # Insert into Pinterest database
        pinterest_conn = sqlite3.connect(self.pinterest_db)
        pinterest_cursor = pinterest_conn.cursor()
        
        try:
            # Check if already exists
            pinterest_cursor.execute(
                'SELECT id FROM products WHERE source_product_id = ?',
                (source_id,)
            )
            if pinterest_cursor.fetchone():
                logger.info("Product 1 already exists in Pinterest database")
                pinterest_conn.close()
                return True
            
            # Extract category from title
            category = None
            if '|' in title:
                category = title.split('|')[-1].strip()
            
            # Insert product 1
            pinterest_cursor.execute('''
                INSERT INTO products (
                    source_product_id, title, affiliate_link, category, scraped_at
                ) VALUES (?, ?, ?, ?, ?)
            ''', (source_id, title, affiliate_link, category, scraped_at))
            
            pinterest_conn.commit()
            pinterest_conn.close()
            
            logger.info("✅ Product 1 copied to Pinterest database")
            return True
            
        except Exception as e:
            logger.error(f"Error copying product 1: {e}")
            pinterest_conn.close()
            return False
    
    async def process_product_1_only(self):
        """Process only product 1 for Pinterest content generation"""
        logger.info("Processing product 1 for Pinterest content generation...")
        
        import sqlite3
        
        conn = sqlite3.connect(self.pinterest_db)
        cursor = conn.cursor()
        
        # Get product 1 (should be the only one)
        cursor.execute('''
            SELECT id, title, affiliate_link, category
            FROM products
            WHERE processed_for_pinterest = FALSE
            LIMIT 1
        ''')
        
        product = cursor.fetchone()
        
        if not product:
            logger.warning("No unprocessed products found (product 1 may already be processed)")
            conn.close()
            return 0
        
        product_id, title, affiliate_link, category = product
        logger.info(f"🎯 Processing Product 1: {title[:50]}...")
        
        try:
            # Generate complete Pinterest content with Pexels video
            content = await self.generate_complete_pinterest_content(
                product_id, title, affiliate_link, category
            )
            
            # Store Pinterest content with research data
            cursor.execute('''
                INSERT INTO pinterest_content (
                    product_id, pinterest_title, pinterest_description, affiliate_link,
                    board_name, tagged_topics, pexels_video_id, pexels_video_url,
                    video_download_url, video_preview_image, video_duration,
                    video_width, video_height, video_search_query,
                    research_summary, research_quality, researched_benefits, researched_features,
                    marketing_angle, search_terms, topic_tags, alt_text
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                product_id,
                content['pinterest_title'],
                content['pinterest_description'],
                content['affiliate_link'],
                content['board_name'],
                content['tagged_topics'],
                content['pexels_video_id'],
                content['pexels_video_url'],
                content['video_download_url'],
                content['video_preview_image'],
                content['video_duration'],
                content['video_width'],
                content['video_height'],
                content['video_search_query'],
                content['research_summary'],
                content['research_quality'],
                content['researched_benefits'],
                content['researched_features'],
                content['marketing_angle'],
                content['search_terms'],
                content['topic_tags'],
                content['alt_text']
            ))
            
            # Mark product as processed
            cursor.execute('''
                UPDATE products
                SET processed_for_pinterest = TRUE, content_generated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (product_id,))
            
            conn.commit()
            conn.close()
            
            logger.info("✅ Product 1 Pinterest content generated successfully!")
            logger.info(f"📝 Title: {content['pinterest_title']}")
            logger.info(f"🎬 Video: {'Yes' if content['video_download_url'] else 'No'}")
            
            return 1
            
        except Exception as e:
            logger.error(f"❌ Error processing product 1: {e}")
            conn.close()
            return 0

async def main():
    """Main function to generate Pinterest content for product 1 only"""
    
    # Check environment
    pexels_key = os.getenv('PEXELS_API_KEY')
    if not pexels_key:
        logger.warning("⚠️ PEXELS_API_KEY not set - video integration will be disabled")
    
    logger.info("🚀 Starting Product 1 Pinterest Content Generation")
    logger.info("=" * 60)
    
    try:
        # Initialize generator
        generator = Product1Generator()
        
        # Copy product 1 from source
        if not generator.copy_product_1_from_source():
            logger.error("❌ Failed to copy product 1 from source database")
            return
        
        # Process product 1
        processed = await generator.process_product_1_only()
        
        if processed > 0:
            logger.info("=" * 60)
            logger.info("🎉 SUCCESS: Product 1 Pinterest content generated!")
            logger.info("🔍 Use 'python3 simple_db_viewer.py' to view the content")
            logger.info("=" * 60)
        else:
            logger.error("❌ Failed to generate content for product 1")
            
    except Exception as e:
        logger.error(f"❌ Error in main process: {e}")

if __name__ == "__main__":
    asyncio.run(main())
