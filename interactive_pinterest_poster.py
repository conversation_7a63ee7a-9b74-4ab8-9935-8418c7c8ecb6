#!/usr/bin/env python3
"""
Interactive Pinterest Poster
Assumes you're already logged in and on the pin-creation-tool page
"""

import sqlite3
import os
import time
from datetime import datetime
from playwright.sync_api import sync_playwright
from loguru import logger
from src.config import Config

class InteractivePinterestPoster:
    def __init__(self):
        self.config = Config()
        # Use the enhanced database that the system creates
        self.database_path = "pinterest_content_enhanced.db"
        self.playwright = None
        self.browser = None
        self.page = None
    
    def connect_to_browser(self):
        """Connect to existing browser (assumes already on pin-creation-tool)"""
        logger.info("🔗 Connecting to your existing browser...")
        
        try:
            self.playwright = sync_playwright().start()
            
            # Try to connect to existing Chrome with debug port
            self.browser = self.playwright.chromium.connect_over_cdp("http://localhost:9222")
            
            # Get the active page
            if self.browser.contexts and self.browser.contexts[0].pages:
                self.page = self.browser.contexts[0].pages[0]
                logger.info("✅ Connected to existing browser")
                
                # Check current URL
                current_url = self.page.url
                logger.info(f"📍 Current page: {current_url}")
                
                if "pin-creation-tool" in current_url:
                    logger.info("✅ Already on pin-creation-tool - ready to post!")
                else:
                    logger.warning("⚠️ Not on pin-creation-tool page")
                
                return True
            else:
                logger.error("❌ No active browser pages found")
                return False
                
        except Exception as e:
            logger.error(f"❌ Failed to connect to browser: {e}")
            logger.info("💡 Make sure Chrome is running with --remote-debugging-port=9222")
            return False
    
    def get_all_products(self):
        """Get all products with their posting status"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT 
                    pc.product_id,
                    p.title as product_title,
                    pc.pinterest_title,
                    pc.pinterest_description,
                    pc.affiliate_link,
                    pc.pexels_video_url,
                    pc.is_used,
                    pc.posted_date
                FROM pinterest_content pc
                JOIN products p ON pc.product_id = p.id
                WHERE pc.pinterest_title IS NOT NULL
                  AND pc.pinterest_description IS NOT NULL
                  AND pc.affiliate_link IS NOT NULL
                ORDER BY p.id ASC
            """)
            
            products = cursor.fetchall()
            conn.close()
            
            columns = ['product_id', 'product_title', 'pinterest_title', 
                      'pinterest_description', 'affiliate_link', 'pexels_video_url',
                      'is_used', 'posted_date']
            return [dict(zip(columns, row)) for row in products]
            
        except Exception as e:
            logger.error(f"❌ Failed to get products: {e}")
            return []
    
    def get_unposted_products(self):
        """Get products that haven't been posted yet"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT 
                    pc.product_id,
                    p.title as product_title,
                    pc.pinterest_title,
                    pc.pinterest_description,
                    pc.affiliate_link,
                    pc.pexels_video_url
                FROM pinterest_content pc
                JOIN products p ON pc.product_id = p.id
                WHERE pc.is_used = FALSE 
                  AND pc.pinterest_title IS NOT NULL
                  AND pc.pinterest_description IS NOT NULL
                  AND pc.affiliate_link IS NOT NULL
                ORDER BY p.id ASC
            """)
            
            products = cursor.fetchall()
            conn.close()
            
            columns = ['product_id', 'product_title', 'pinterest_title', 
                      'pinterest_description', 'affiliate_link', 'pexels_video_url']
            return [dict(zip(columns, row)) for row in products]
            
        except Exception as e:
            logger.error(f"❌ Failed to get unposted products: {e}")
            return []
    
    def reset_posting_status(self):
        """Reset all products to unposted status"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE pinterest_content 
                SET is_used = FALSE, posted_date = NULL
                WHERE pinterest_title IS NOT NULL
            """)
            
            conn.commit()
            affected_rows = cursor.rowcount
            conn.close()
            
            logger.info(f"✅ Reset posting status for {affected_rows} products")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to reset posting status: {e}")
            return False
    
    def mark_product_posted(self, product_id):
        """Mark a product as posted"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE pinterest_content 
                SET is_used = TRUE, posted_date = ?
                WHERE product_id = ?
            """, (datetime.now().isoformat(), product_id))
            
            conn.commit()
            conn.close()
            
            logger.info(f"✅ Marked product {product_id} as posted")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to mark product as posted: {e}")
            return False
    
    def display_product_menu(self):
        """Display interactive product selection menu"""
        while True:
            print("\n" + "="*60)
            print("🎯 PINTEREST INTERACTIVE POSTER")
            print("="*60)
            
            # Get product counts
            all_products = self.get_all_products()
            unposted_products = self.get_unposted_products()
            
            posted_count = len(all_products) - len(unposted_products)
            
            print(f"📊 Status: {posted_count} posted, {len(unposted_products)} remaining")
            print()
            
            if unposted_products:
                print("📌 NEXT PRODUCTS TO POST:")
                for i, product in enumerate(unposted_products[:5], 1):
                    print(f"   {i}. Product #{product['product_id']}: {product['product_title'][:50]}...")
                print()
            
            print("🔧 OPTIONS:")
            if unposted_products:
                print("   1-5: Post specific product")
                print("   n: Post next product")
            print("   v: View all products")
            print("   r: Reset all posting status")
            print("   q: Quit")
            print()
            
            choice = input("Choose option: ").strip().lower()
            
            if choice == 'q':
                break
            elif choice == 'n' and unposted_products:
                return unposted_products[0]
            elif choice.isdigit() and 1 <= int(choice) <= min(5, len(unposted_products)):
                return unposted_products[int(choice) - 1]
            elif choice == 'v':
                self.view_all_products()
            elif choice == 'r':
                if input("Reset all posting status? (y/N): ").lower() == 'y':
                    self.reset_posting_status()
            else:
                print("❌ Invalid choice")
        
        return None
    
    def view_all_products(self):
        """View all products with their status"""
        products = self.get_all_products()
        
        print("\n" + "="*80)
        print("📋 ALL PRODUCTS")
        print("="*80)
        
        for product in products:
            status = "✅ POSTED" if product['is_used'] else "⏳ PENDING"
            posted_date = product['posted_date'] if product['posted_date'] else "Never"
            
            print(f"#{product['product_id']:2d} | {status} | {product['product_title'][:40]:40s} | {posted_date}")
        
        print("="*80)
        input("Press Enter to continue...")

    def fill_pinterest_form(self, product):
        """Fill the Pinterest form with product data"""
        try:
            logger.info("🔄 Filling Pinterest form...")

            # Fill title
            logger.info("📝 Adding title...")
            try:
                self.page.fill('textbox[placeholder="Add your title"]', product['pinterest_title'])
                logger.info("✅ Title added")
            except Exception as e:
                logger.warning(f"⚠️ Could not fill title: {e}")

            # Fill description
            logger.info("📝 Adding description...")
            try:
                # Click the text editor button to enable it
                self.page.click('button:has-text("Text editor")')
                time.sleep(1)

                # Fill the description
                self.page.fill('combobox[placeholder*="Tell everyone what your Pin is about"]', product['pinterest_description'])
                logger.info("✅ Description added")
            except Exception as e:
                logger.warning(f"⚠️ Could not fill description: {e}")

            # Add affiliate link
            logger.info("🔗 Adding affiliate link...")
            try:
                self.page.fill('textbox[placeholder="Add a destination link"]', product['affiliate_link'])
                logger.info("✅ Affiliate link added")
            except Exception as e:
                logger.warning(f"⚠️ Could not add affiliate link: {e}")

            logger.info("✅ Pinterest form filled successfully!")
            logger.info("👀 Please review the form and click 'Publish' manually")

            return True

        except Exception as e:
            logger.error(f"❌ Failed to fill Pinterest form: {e}")
            return False

def main():
    """Main interactive posting function"""
    poster = InteractivePinterestPoster()
    
    # Connect to browser
    if not poster.connect_to_browser():
        logger.error("❌ Could not connect to browser")
        return
    
    # Interactive menu
    while True:
        selected_product = poster.display_product_menu()
        
        if not selected_product:
            break
        
        print(f"\n🎯 Selected: Product #{selected_product['product_id']} - {selected_product['product_title']}")
        print(f"📝 Title: {selected_product['pinterest_title']}")
        print(f"📄 Description: {selected_product['pinterest_description'][:100]}...")
        print(f"🔗 Link: {selected_product['affiliate_link']}")
        
        if input("\nFill Pinterest form with this product? (Y/n): ").lower() != 'n':
            success = poster.fill_pinterest_form(selected_product)

            if success:
                print("✅ Form filled! Please review and post manually.")
                if input("Mark as posted? (Y/n): ").lower() != 'n':
                    poster.mark_product_posted(selected_product['product_id'])
            else:
                print("❌ Failed to fill form")
    
    logger.info("👋 Pinterest posting session complete!")

if __name__ == "__main__":
    main()
