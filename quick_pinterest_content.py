#!/usr/bin/env python3
"""
Quick Pinterest Content Provider
Just shows you the content to copy-paste manually - no slow automation
"""

import sqlite3
import os
from datetime import datetime
from loguru import logger

class QuickPinterestContent:
    def __init__(self):
        self.database_path = "pinterest_content_enhanced.db"
    
    def get_unposted_products(self):
        """Get products that haven't been posted yet"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT 
                    pc.product_id,
                    p.title as product_title,
                    pc.pinterest_title,
                    pc.pinterest_description,
                    pc.affiliate_link,
                    pc.pexels_video_url
                FROM pinterest_content pc
                JOIN products p ON pc.product_id = p.id
                WHERE pc.is_used = FALSE 
                  AND pc.pinterest_title IS NOT NULL
                  AND pc.pinterest_description IS NOT NULL
                  AND pc.affiliate_link IS NOT NULL
                ORDER BY p.id ASC
            """)
            
            products = cursor.fetchall()
            conn.close()
            
            columns = ['product_id', 'product_title', 'pinterest_title', 
                      'pinterest_description', 'affiliate_link', 'pexels_video_url']
            return [dict(zip(columns, row)) for row in products]
            
        except Exception as e:
            logger.error(f"❌ Failed to get unposted products: {e}")
            return []
    
    def mark_product_posted(self, product_id):
        """Mark a product as posted"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE pinterest_content 
                SET is_used = TRUE, posted_date = ?
                WHERE product_id = ?
            """, (datetime.now().isoformat(), product_id))
            
            conn.commit()
            conn.close()
            
            logger.info(f"✅ Marked product {product_id} as posted")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to mark product as posted: {e}")
            return False
    
    def display_content_for_manual_posting(self, product):
        """Display content for manual copy-paste"""
        print("\n" + "="*80)
        print(f"📌 PRODUCT #{product['product_id']}: {product['product_title']}")
        print("="*80)
        
        print("\n📝 TITLE (copy this):")
        print("-" * 40)
        print(product['pinterest_title'])
        
        print("\n📄 DESCRIPTION (copy this):")
        print("-" * 40)
        print(product['pinterest_description'])
        
        print("\n🔗 AFFILIATE LINK (copy this):")
        print("-" * 40)
        print(product['affiliate_link'])
        
        if product.get('pexels_video_url'):
            print("\n🎬 PEXELS VIDEO URL (for manual download):")
            print("-" * 40)
            print(product['pexels_video_url'])
        
        print("\n" + "="*80)
        print("📋 MANUAL STEPS:")
        print("1. Upload image/video to Pinterest")
        print("2. Copy-paste TITLE above")
        print("3. Copy-paste DESCRIPTION above") 
        print("4. Copy-paste AFFILIATE LINK above")
        print("5. Click 'Publish'")
        print("="*80)

def main():
    """Main function - quick content display"""
    print("🚀 Quick Pinterest Content Provider")
    print("No slow automation - just fast content display!")
    
    provider = QuickPinterestContent()
    
    while True:
        # Get unposted products
        products = provider.get_unposted_products()
        
        if not products:
            print("\n✅ No unposted products found!")
            break
        
        print(f"\n📊 Found {len(products)} unposted products")
        
        # Show menu
        print("\n🔧 OPTIONS:")
        print("1. Show next product content")
        print("2. Show specific product (enter number)")
        print("3. Mark product as posted")
        print("4. View all products")
        print("5. Quit")
        
        choice = input("\nChoose option (1-5): ").strip()
        
        if choice == '1':
            # Show first unposted product
            product = products[0]
            provider.display_content_for_manual_posting(product)
            
        elif choice == '2':
            # Show specific product
            try:
                product_num = int(input("Enter product number: "))
                product = next((p for p in products if p['product_id'] == product_num), None)
                if product:
                    provider.display_content_for_manual_posting(product)
                else:
                    print(f"❌ Product #{product_num} not found or already posted")
            except ValueError:
                print("❌ Invalid product number")
                
        elif choice == '3':
            # Mark as posted
            try:
                product_num = int(input("Enter product number to mark as posted: "))
                if provider.mark_product_posted(product_num):
                    print(f"✅ Product #{product_num} marked as posted!")
                else:
                    print(f"❌ Failed to mark product #{product_num}")
            except ValueError:
                print("❌ Invalid product number")
                
        elif choice == '4':
            # View all products
            print("\n📋 ALL UNPOSTED PRODUCTS:")
            for i, product in enumerate(products, 1):
                print(f"{i:2d}. Product #{product['product_id']}: {product['product_title']}")
                
        elif choice == '5':
            break
            
        else:
            print("❌ Invalid choice")
    
    print("\n👋 Quick Pinterest Content Provider - Done!")

if __name__ == "__main__":
    main()
