#!/usr/bin/env python3
"""
Simple Database Viewer - Browse Pinterest content
"""

import sqlite3
from datetime import datetime

def get_products():
    """Get all products with Pinterest content"""
    conn = sqlite3.connect('pinterest_content_enhanced.db')
    cursor = conn.cursor()
    
    cursor.execute("""
        SELECT 
            product_id,
            pinterest_title,
            pinterest_description,
            affiliate_link,
            pexels_video_url,
            is_used,
            posted_at
        FROM pinterest_content 
        WHERE pinterest_title IS NOT NULL
        ORDER BY product_id ASC
    """)
    
    products = cursor.fetchall()
    conn.close()
    
    return products

def mark_posted(product_id):
    """Mark product as posted"""
    conn = sqlite3.connect('pinterest_content_enhanced.db')
    cursor = conn.cursor()
    
    cursor.execute("""
        UPDATE pinterest_content 
        SET is_used = TRUE, posted_at = ?
        WHERE product_id = ?
    """, (datetime.now().isoformat(), product_id))
    
    conn.commit()
    conn.close()
    return True

def mark_unposted(product_id):
    """Mark product as unposted"""
    conn = sqlite3.connect('pinterest_content_enhanced.db')
    cursor = conn.cursor()
    
    cursor.execute("""
        UPDATE pinterest_content 
        SET is_used = FALSE, posted_at = NULL
        WHERE product_id = ?
    """, (product_id,))
    
    conn.commit()
    conn.close()
    return True

def show_product_list(products):
    """Show list of all products"""
    print("\n" + "="*80)
    print("📋 PINTEREST PRODUCTS DATABASE")
    print("="*80)
    print(f"{'ID':>3} | {'STATUS':^8} | {'TITLE':^60}")
    print("-" * 80)
    
    for product in products:
        product_id, title, desc, link, video, is_used, posted_at = product
        status = "✅ POSTED" if is_used else "⏳ PENDING"
        title_short = title[:57] + "..." if len(title) > 60 else title
        
        print(f"{product_id:>3} | {status:^8} | {title_short:<60}")
    
    print("="*80)

def show_product_detail(product):
    """Show detailed product info for copy-paste"""
    product_id, title, desc, link, video, is_used, posted_at = product
    status = "✅ POSTED" if is_used else "⏳ PENDING"
    posted_date = posted_at[:19] if posted_at else "Never"
    
    print("\n" + "="*80)
    print(f"📌 PRODUCT #{product_id}")
    print(f"Status: {status} | Posted: {posted_date}")
    print("="*80)
    
    print("\n📝 TITLE (copy this):")
    print("-" * 40)
    print(title)
    
    print("\n📄 DESCRIPTION (copy this):")
    print("-" * 40)
    print(desc)
    
    print("\n🔗 AFFILIATE LINK (copy this):")
    print("-" * 40)
    print(link)
    
    if video:
        print("\n🎬 PEXELS VIDEO URL:")
        print("-" * 40)
        print(video)
    
    print("\n" + "="*80)
    print("📋 COPY-PASTE STEPS:")
    print("1. Upload image/video to Pinterest")
    print("2. Copy-paste TITLE above")
    print("3. Copy-paste DESCRIPTION above")
    print("4. Copy-paste AFFILIATE LINK above")
    print("5. Click 'Publish'")
    print("="*80)

def main():
    """Main viewer"""
    print("🗄️ Simple Pinterest Database Viewer")
    
    while True:
        products = get_products()
        
        if not products:
            print("\n❌ No products found!")
            break
        
        unposted = [p for p in products if not p[5]]  # is_used is index 5
        posted = [p for p in products if p[5]]
        
        print(f"\n📊 Status: {len(products)} total | {len(posted)} posted | {len(unposted)} pending")
        
        print("\n🔧 OPTIONS:")
        print("1. List all products")
        print("2. View product details (enter ID)")
        print("3. List only unposted products")
        print("4. Mark product as posted")
        print("5. Mark product as unposted")
        print("6. Quit")
        
        choice = input("\nChoose (1-6): ").strip()
        
        if choice == '1':
            show_product_list(products)
            
        elif choice == '2':
            try:
                pid = int(input("Enter product ID: "))
                product = next((p for p in products if p[0] == pid), None)
                if product:
                    show_product_detail(product)
                    input("\nPress Enter to continue...")
                else:
                    print(f"❌ Product #{pid} not found")
            except ValueError:
                print("❌ Invalid ID")
                
        elif choice == '3':
            if unposted:
                show_product_list(unposted)
            else:
                print("\n✅ No unposted products!")
                
        elif choice == '4':
            try:
                pid = int(input("Mark as posted - Enter product ID: "))
                if mark_posted(pid):
                    print(f"✅ Product #{pid} marked as posted!")
            except ValueError:
                print("❌ Invalid ID")
                
        elif choice == '5':
            try:
                pid = int(input("Mark as unposted - Enter product ID: "))
                if mark_unposted(pid):
                    print(f"✅ Product #{pid} marked as unposted!")
            except ValueError:
                print("❌ Invalid ID")
                
        elif choice == '6':
            break
            
        else:
            print("❌ Invalid choice")
    
    print("\n👋 Database Viewer - Done!")

if __name__ == "__main__":
    main()
