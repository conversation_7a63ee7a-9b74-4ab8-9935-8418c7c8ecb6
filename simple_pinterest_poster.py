#!/usr/bin/env python3
"""
Simple Pinterest Poster - Works with existing browser session
Assumes you're already logged in and on pin-creation-tool page
"""

import sqlite3
import os
import time
import requests
import re
from datetime import datetime
from playwright.sync_api import sync_playwright
from loguru import logger
from src.config import Config
from PIL import Image, ImageDraw, ImageFont

class SimplePinterestPoster:
    def __init__(self):
        self.config = Config()
        self.database_path = "pinterest_content_enhanced.db"
        self.playwright = None
        self.browser = None
        self.page = None
    
    def start_new_browser(self):
        """Start a new browser session"""
        logger.info("🚀 Starting new browser session...")
        
        try:
            self.playwright = sync_playwright().start()
            
            # Launch regular browser
            self.browser = self.playwright.chromium.launch(
                headless=False,
                args=[
                    '--disable-blink-features=AutomationControlled',
                    '--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
                ]
            )
            
            self.context = self.browser.new_context(
                viewport={'width': 1280, 'height': 720}
            )
            
            self.page = self.context.new_page()
            logger.info("✅ New browser session started")
            
            # Navigate to Pinterest pin creation tool
            logger.info("📍 Navigating to Pinterest pin-creation-tool...")
            self.page.goto("https://www.pinterest.com/pin-creation-tool/")
            self.page.wait_for_load_state("networkidle")
            
            logger.info("🔑 Please log into Pinterest manually in the browser window")
            logger.info("   Then press Enter to continue...")
            input("Press Enter after you're logged in and on pin-creation-tool page...")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to start browser: {e}")
            return False

    def download_pexels_video(self, pexels_url, product_id):
        """Download video from Pexels URL"""
        try:
            logger.info(f"📥 Downloading video from Pexels for product {product_id}...")
            logger.info(f"🔗 Pexels URL: {pexels_url}")

            # Use the browser to get the page (avoids 403 errors)
            temp_page = self.browser.new_page()
            temp_page.goto(pexels_url)
            temp_page.wait_for_load_state("networkidle")
            time.sleep(3)  # Wait for dynamic content

            # Look for download button and click it to get the actual video URL
            try:
                # Try to find and click the download button
                download_selectors = [
                    'button:has-text("Free Download")',
                    'a:has-text("Free Download")',
                    '[data-test-id="download-button"]',
                    'button[aria-label*="Download"]'
                ]

                download_clicked = False
                for selector in download_selectors:
                    try:
                        temp_page.click(selector, timeout=5000)
                        download_clicked = True
                        logger.info("✅ Clicked download button")
                        break
                    except:
                        continue

                if download_clicked:
                    # Wait for download options to appear
                    time.sleep(2)

                    # Look for video download links
                    video_links = temp_page.query_selector_all('a[href*=".mp4"]')
                    if video_links:
                        video_url = video_links[0].get_attribute('href')
                        logger.info(f"✅ Found video URL: {video_url}")
                    else:
                        raise Exception("No video download links found")
                else:
                    raise Exception("Could not find download button")

            except Exception as e:
                logger.warning(f"⚠️ Download button approach failed: {e}")

                # Fallback: Try to extract from page source
                page_content = temp_page.content()

                # Look for video URLs in the page content
                video_patterns = [
                    r'"url":"(https://[^"]*videos[^"]*\.mp4[^"]*)"',
                    r'href="(https://[^"]*\.mp4[^"]*)"',
                    r'"src":"(https://[^"]*\.mp4[^"]*)"',
                    r'data-src="(https://[^"]*\.mp4[^"]*)"'
                ]

                video_url = None
                for pattern in video_patterns:
                    matches = re.findall(pattern, page_content)
                    if matches:
                        # Filter out non-Pexels URLs (avoid Canva, etc.)
                        for match in matches:
                            clean_url = match.replace('\\/', '/')
                            if 'pexels' in clean_url.lower() or 'videos.pexels.com' in clean_url.lower():
                                video_url = clean_url
                                break
                        if video_url:
                            break

                if not video_url:
                    raise Exception("Could not find Pexels video URL")

            temp_page.close()

            # Download the video
            logger.info(f"📥 Downloading video from: {video_url}")

            headers = {
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
                'Referer': pexels_url
            }

            response = requests.get(video_url, headers=headers, stream=True, timeout=30)
            response.raise_for_status()

            # Save video file
            video_filename = f"temp_video_{product_id}.mp4"
            video_path = os.path.join(os.getcwd(), video_filename)

            with open(video_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)

            # Verify file was downloaded
            if os.path.exists(video_path) and os.path.getsize(video_path) > 1000:  # At least 1KB
                logger.info(f"✅ Video downloaded successfully: {video_path}")
                return video_path
            else:
                raise Exception("Downloaded file is too small or doesn't exist")

        except Exception as e:
            logger.warning(f"⚠️ Video download failed: {e}")
            logger.info("🔄 Creating fallback image instead...")
            return self.create_fallback_image(product_id)

    def create_fallback_image(self, product_id):
        """Create a Pinterest-optimized fallback image"""
        try:
            logger.info(f"🎨 Creating fallback image for product {product_id}...")

            # Pinterest optimal size: 1000x1500 (2:3 ratio)
            width, height = 1000, 1500

            # Create image with gradient background
            image = Image.new('RGB', (width, height), color='#1a1a1a')
            draw = ImageDraw.Draw(image)

            # Add gradient effect
            for y in range(height):
                color_value = int(26 + (y / height) * 100)  # Gradient from dark to lighter
                color = (color_value, color_value, color_value)
                draw.line([(0, y), (width, y)], fill=color)

            # Add text
            try:
                # Try to use a system font
                font_large = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 80)
                font_small = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 40)
            except:
                # Fallback to default font
                font_large = ImageFont.load_default()
                font_small = ImageFont.load_default()

            # Add main text
            text = f"Product #{product_id}"
            text_bbox = draw.textbbox((0, 0), text, font=font_large)
            text_width = text_bbox[2] - text_bbox[0]
            text_height = text_bbox[3] - text_bbox[1]

            x = (width - text_width) // 2
            y = height // 2 - 100

            draw.text((x, y), text, fill='white', font=font_large)

            # Add subtitle
            subtitle = "Pinterest Pin"
            subtitle_bbox = draw.textbbox((0, 0), subtitle, font=font_small)
            subtitle_width = subtitle_bbox[2] - subtitle_bbox[0]

            x_sub = (width - subtitle_width) // 2
            y_sub = y + text_height + 20

            draw.text((x_sub, y_sub), subtitle, fill='#cccccc', font=font_small)

            # Save image
            image_filename = f"temp_image_{product_id}.png"
            image_path = os.path.join(os.getcwd(), image_filename)
            image.save(image_path, 'PNG', quality=95)

            logger.info(f"✅ Fallback image created: {image_path}")
            return image_path

        except Exception as e:
            logger.error(f"❌ Failed to create fallback image: {e}")
            return None

    def get_unposted_products(self):
        """Get products that haven't been posted yet"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT 
                    pc.product_id,
                    p.title as product_title,
                    pc.pinterest_title,
                    pc.pinterest_description,
                    pc.affiliate_link,
                    pc.pexels_video_url
                FROM pinterest_content pc
                JOIN products p ON pc.product_id = p.id
                WHERE pc.is_used = FALSE 
                  AND pc.pinterest_title IS NOT NULL
                  AND pc.pinterest_description IS NOT NULL
                  AND pc.affiliate_link IS NOT NULL
                ORDER BY p.id ASC
                LIMIT 5
            """)
            
            products = cursor.fetchall()
            conn.close()
            
            columns = ['product_id', 'product_title', 'pinterest_title', 
                      'pinterest_description', 'affiliate_link', 'pexels_video_url']
            return [dict(zip(columns, row)) for row in products]
            
        except Exception as e:
            logger.error(f"❌ Failed to get unposted products: {e}")
            return []
    
    def fill_pinterest_form(self, product):
        """Fill the Pinterest form with product data"""
        try:
            logger.info(f"🔄 Filling Pinterest form for: {product['product_title']}")

            # First, check if we need to upload media
            logger.info("📋 Checking Pinterest form state...")

            # Wait a moment for page to be ready
            time.sleep(2)

            # Check if title field is available (means media is uploaded)
            title_available = False
            try:
                title_element = self.page.locator('textbox[placeholder="Add your title"]')
                if title_element.is_visible():
                    title_available = True
                    logger.info("✅ Title field is available - media already uploaded")
                else:
                    logger.info("⚠️ Title field not available - need to upload media first")
            except:
                logger.info("⚠️ Title field not found - need to upload media first")

            if not title_available:
                logger.info("📤 Please upload an image or video first, then press Enter to continue...")
                input("Press Enter after uploading media...")
                time.sleep(2)

            # Fill title
            logger.info("📝 Adding title...")
            try:
                # Try multiple title selectors
                title_selectors = [
                    'textbox[placeholder="Add your title"]',
                    'input[placeholder="Add your title"]',
                    'textarea[placeholder="Add your title"]'
                ]

                title_filled = False
                for selector in title_selectors:
                    try:
                        self.page.fill(selector, product['pinterest_title'])
                        title_filled = True
                        logger.info("✅ Title added")
                        break
                    except:
                        continue

                if not title_filled:
                    logger.warning("⚠️ Could not fill title with any selector")

            except Exception as e:
                logger.warning(f"⚠️ Could not fill title: {e}")

            # Fill description
            logger.info("📝 Adding description...")
            try:
                # Try different description selectors
                description_selectors = [
                    'combobox[placeholder*="Tell everyone what your Pin is about"]',
                    'textarea[placeholder*="Tell everyone what your Pin is about"]',
                    '[data-test-id="pin-draft-description"]',
                    'div[contenteditable="true"]'
                ]

                description_filled = False
                for selector in description_selectors:
                    try:
                        self.page.fill(selector, product['pinterest_description'])
                        description_filled = True
                        logger.info("✅ Description added")
                        break
                    except:
                        continue

                if not description_filled:
                    logger.warning("⚠️ Could not fill description with any selector")

            except Exception as e:
                logger.warning(f"⚠️ Could not fill description: {e}")

            # Add affiliate link
            logger.info("🔗 Adding affiliate link...")
            try:
                link_selectors = [
                    'textbox[placeholder="Add a destination link"]',
                    'input[placeholder="Add a destination link"]',
                    'input[type="url"]'
                ]

                link_filled = False
                for selector in link_selectors:
                    try:
                        self.page.fill(selector, product['affiliate_link'])
                        link_filled = True
                        logger.info("✅ Affiliate link added")
                        break
                    except:
                        continue

                if not link_filled:
                    logger.warning("⚠️ Could not add affiliate link with any selector")

            except Exception as e:
                logger.warning(f"⚠️ Could not add affiliate link: {e}")

            logger.info("✅ Pinterest form filled successfully!")
            logger.info("👀 Please review the form and click 'Publish' manually when ready")

            return True

        except Exception as e:
            logger.error(f"❌ Failed to fill Pinterest form: {e}")
            return False

    def upload_media_and_fill_form(self, product):
        """Download media, upload it, then fill the Pinterest form"""
        try:
            logger.info(f"🔄 Processing: {product['product_title']}")

            # Step 1: Download media from Pexels URL
            media_path = None
            if product.get('pexels_video_url'):
                logger.info("📥 Downloading media from Pexels...")
                media_path = self.download_pexels_video(product['pexels_video_url'], product['product_id'])
            else:
                logger.info("🎨 No Pexels URL found, creating fallback image...")
                media_path = self.create_fallback_image(product['product_id'])

            if not media_path:
                logger.error("❌ Could not get media file")
                return False

            # Step 2: Upload media to Pinterest
            logger.info("📤 Uploading media to Pinterest...")
            try:
                # Handle file upload using file chooser
                with self.page.expect_file_chooser() as fc_info:
                    # Try multiple selectors for the upload trigger
                    upload_selectors = [
                        'text="Drag and drop or click to upload images or a video"',
                        'button:has-text("File upload")',
                        'input[type="file"]'
                    ]

                    upload_clicked = False
                    for selector in upload_selectors:
                        try:
                            self.page.click(selector, timeout=5000)
                            upload_clicked = True
                            logger.info(f"✅ Clicked upload trigger")
                            break
                        except:
                            continue

                    if not upload_clicked:
                        # Try clicking the upload area directly
                        self.page.click('img[alt="Add an image or video"]', timeout=5000)
                        logger.info("✅ Clicked upload area")

                # Handle the file chooser
                file_chooser = fc_info.value
                file_chooser.set_files(media_path)

                logger.info("✅ Media upload initiated")

                # Wait for upload to complete
                logger.info("⏳ Waiting for upload to complete...")
                time.sleep(10)  # Give time for upload

            except Exception as e:
                logger.error(f"❌ Media upload failed: {e}")
                return False

            # Step 3: Wait for form fields to become available
            logger.info("⏳ Waiting for form fields to become available...")
            time.sleep(3)

            # Step 3: Fill title
            logger.info("📝 Adding title...")
            try:
                # Wait for title field to be available
                title_selectors = [
                    'textbox[placeholder="Add your title"]',
                    'input[placeholder="Add your title"]',
                    'textarea[placeholder="Add your title"]',
                    'input[placeholder*="title"]',
                    '[data-test-id="pin-draft-title"]'
                ]

                title_filled = False
                for selector in title_selectors:
                    try:
                        # Wait for the element to be visible
                        self.page.wait_for_selector(selector, timeout=10000)

                        # Clear any existing content
                        self.page.fill(selector, "")
                        time.sleep(0.5)

                        # Fill with new content
                        self.page.fill(selector, product['pinterest_title'])

                        # Verify it was filled
                        filled_value = self.page.input_value(selector)
                        if filled_value and len(filled_value) > 0:
                            title_filled = True
                            logger.info("✅ Title added successfully")
                            break

                    except Exception as e:
                        logger.debug(f"Title selector {selector} failed: {e}")
                        continue

                if not title_filled:
                    logger.warning("⚠️ Could not fill title automatically")
                    logger.info(f"📝 Title to add manually: {product['pinterest_title']}")

            except Exception as e:
                logger.warning(f"⚠️ Title filling error: {e}")

            # Step 4: Fill description
            logger.info("📝 Adding description...")
            try:
                # Try different description selectors
                description_selectors = [
                    'combobox[placeholder*="Tell everyone what your Pin is about"]',
                    'textarea[placeholder*="Tell everyone what your Pin is about"]',
                    'div[contenteditable="true"]',
                    '[data-test-id="pin-draft-description"]',
                    'textarea[placeholder*="description"]'
                ]

                description_filled = False
                for selector in description_selectors:
                    try:
                        # Wait for the element
                        self.page.wait_for_selector(selector, timeout=10000)

                        # Try clicking first to focus
                        self.page.click(selector)
                        time.sleep(0.5)

                        # Clear and fill
                        self.page.fill(selector, "")
                        time.sleep(0.5)
                        self.page.fill(selector, product['pinterest_description'])

                        # For contenteditable divs, try typing instead
                        if 'contenteditable' in selector:
                            self.page.click(selector)
                            self.page.keyboard.press('Control+a')  # Select all
                            self.page.keyboard.type(product['pinterest_description'])

                        description_filled = True
                        logger.info("✅ Description added successfully")
                        break

                    except Exception as e:
                        logger.debug(f"Description selector {selector} failed: {e}")
                        continue

                if not description_filled:
                    logger.warning("⚠️ Could not fill description automatically")
                    logger.info(f"📄 Description to add manually: {product['pinterest_description'][:100]}...")

            except Exception as e:
                logger.warning(f"⚠️ Description filling error: {e}")

            # Step 5: Add affiliate link
            logger.info("🔗 Adding affiliate link...")
            try:
                link_selectors = [
                    'textbox[placeholder="Add a destination link"]',
                    'input[placeholder="Add a destination link"]',
                    'input[type="url"]',
                    'input[placeholder*="link"]',
                    '[data-test-id="pin-draft-link"]'
                ]

                link_filled = False
                for selector in link_selectors:
                    try:
                        # Wait for the element
                        self.page.wait_for_selector(selector, timeout=10000)

                        # Clear and fill
                        self.page.fill(selector, "")
                        time.sleep(0.5)
                        self.page.fill(selector, product['affiliate_link'])

                        # Verify it was filled
                        filled_value = self.page.input_value(selector)
                        if filled_value and len(filled_value) > 0:
                            link_filled = True
                            logger.info("✅ Affiliate link added successfully")
                            break

                    except Exception as e:
                        logger.debug(f"Link selector {selector} failed: {e}")
                        continue

                if not link_filled:
                    logger.warning("⚠️ Could not add affiliate link automatically")
                    logger.info(f"🔗 Link to add manually: {product['affiliate_link']}")

            except Exception as e:
                logger.warning(f"⚠️ Link filling error: {e}")

            logger.info("✅ Form filling complete!")
            logger.info("👀 Please review all fields and click 'Publish' when ready")

            # Clean up temporary media file
            try:
                if media_path and os.path.exists(media_path):
                    os.remove(media_path)
                    logger.info("🗑️ Temporary media file cleaned up")
            except Exception as e:
                logger.debug(f"Cleanup warning: {e}")

            return True

        except Exception as e:
            logger.error(f"❌ Failed to process Pinterest form: {e}")

            # Clean up temporary media file on error
            try:
                if 'media_path' in locals() and media_path and os.path.exists(media_path):
                    os.remove(media_path)
                    logger.info("🗑️ Temporary media file cleaned up")
            except Exception as cleanup_e:
                logger.debug(f"Cleanup warning: {cleanup_e}")

            return False

    def mark_product_posted(self, product_id):
        """Mark a product as posted"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE pinterest_content 
                SET is_used = TRUE, posted_date = ?
                WHERE product_id = ?
            """, (datetime.now().isoformat(), product_id))
            
            conn.commit()
            conn.close()
            
            logger.info(f"✅ Marked product {product_id} as posted")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to mark product as posted: {e}")
            return False

def main():
    """Main function - NEVER closes browser"""
    logger.info("🎯 Simple Pinterest Poster Starting...")

    poster = SimplePinterestPoster()

    # Start browser session
    if not poster.start_new_browser():
        logger.error("❌ Could not start browser session")
        return

    try:
        while True:
            # Get unposted products
            products = poster.get_unposted_products()

            if not products:
                logger.info("ℹ️ No unposted products found")
                break

            logger.info(f"📊 Found {len(products)} unposted products")

            # Process first product
            product = products[0]
            logger.info(f"🎯 Processing: Product #{product['product_id']} - {product['product_title']}")

            # Upload media and fill the form
            if poster.upload_media_and_fill_form(product):
                logger.info("✅ Form filled successfully!")

                # Ask user to confirm posting
                response = input("\nDid you successfully post this pin? (y/N): ").lower()
                if response == 'y':
                    poster.mark_product_posted(product['product_id'])
                    logger.info("🎉 Product marked as posted!")
                else:
                    logger.info("ℹ️ Product not marked as posted")

            # Ask if user wants to continue
            continue_response = input("\nProcess next product? (Y/n): ").lower()
            if continue_response == 'n':
                break

        logger.info("👋 Pinterest posting session complete!")
        logger.info("🔒 BROWSER WILL REMAIN OPEN - Do not close this terminal!")

        # Keep the script running to prevent browser closure
        input("Press Enter to exit (browser will stay open)...")

    except KeyboardInterrupt:
        logger.info("👋 Session interrupted by user")
        logger.info("🔒 BROWSER WILL REMAIN OPEN")
    except Exception as e:
        logger.error(f"❌ Error: {e}")
        logger.info("🔒 BROWSER WILL REMAIN OPEN")

    # NEVER close the browser - just exit the script
    logger.info("🔒 Script exiting but browser stays open!")

if __name__ == "__main__":
    main()
