#!/usr/bin/env python3
"""
Enhanced Pinterest Content Generator with Pexels Video Integration
Creates Pinterest-ready content with videos from Pexels API based on product data
Maps to Pinterest pin creation form fields exactly
"""

import sqlite3
import json
import asyncio
import os
import requests
import hashlib
import re
from datetime import datetime
from pathlib import Path
from loguru import logger
from typing import Dict, List, Optional
import aiohttp
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Import research tools
import subprocess
import sys

# AI Configuration (matching your existing setup)
class AIConfig:
    """AI configuration matching your existing system"""
    def __init__(self):
        self.ollama_host = 'http://localhost:11434'
        self.model = 'llama3.1:8b'  # Reliable model for content generation
        self.temperature = 0.7
        self.max_tokens = 500  # Increased for more powerful model and better descriptions

        # Pinterest compliance - prohibited terms
        self.prohibited_terms = [
            'commission', 'affiliate', 'affiliate link', 'earn money', 'make money',
            'referral', 'referral link', 'promo code', 'discount code', 'cashback',
            'earn cash', 'get paid', 'commission rate', 'affiliate program',
            'marketing', 'promote', 'advertisement', 'sponsored', 'paid promotion',
            'click here', 'buy now', 'purchase', 'sale', 'discount', 'deal',
            'limited time', 'act now', 'hurry', 'urgent', 'exclusive offer'
        ]

        # Research configuration
        self.enable_research = True  # Enable product research for better content
        self.max_research_results = 3  # Number of search results to analyze
        self.research_timeout = 30  # Timeout for research operations

class EnhancedPinterestGenerator:
    """
    Enhanced Pinterest Content Generator with Pexels Video Integration and Local AI
    Creates complete Pinterest pin data including videos from Pexels API and AI-generated content
    """

    def __init__(self,
                 source_db: str = "digistore24_products.db",
                 pinterest_db: str = "pinterest_content_enhanced.db"):
        self.source_db = source_db
        self.pinterest_db = pinterest_db
        self.processed_count = 0

        # Initialize AI configuration (matching your existing system)
        self.ai_config = AIConfig()

        # Get Pexels API key from environment
        self.pexels_api_key = os.getenv('PEXELS_API_KEY')
        if not self.pexels_api_key:
            logger.warning("PEXELS_API_KEY not found in environment. Video integration disabled.")

        # Ensure source database exists
        if not Path(self.source_db).exists():
            raise FileNotFoundError(f"Source database not found: {self.source_db}")

        # Initialize Pinterest content database
        self._init_pinterest_database()

        # Pre-warm the AI model
        self._warm_up_ai_model()

        logger.info(f"Enhanced Pinterest Generator with AI initialized")
        logger.info(f"Source: {self.source_db}")
        logger.info(f"Pinterest DB: {self.pinterest_db}")
        logger.info(f"AI Model: {self.ai_config.model}")
        logger.info(f"Pexels API: {'Enabled' if self.pexels_api_key else 'Disabled'}")
    
    def _init_pinterest_database(self):
        """Initialize Pinterest content database with enhanced schema"""
        conn = sqlite3.connect(self.pinterest_db)
        cursor = conn.cursor()
        
        # Products table (copy of source data)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                source_product_id INTEGER,
                title TEXT NOT NULL,
                affiliate_link TEXT,
                category TEXT,
                scraped_at TIMESTAMP,
                processed_for_pinterest BOOLEAN DEFAULT FALSE,
                content_generated_at TIMESTAMP
            )
        ''')
        
        # Pinterest content table - mapped to Pinterest form fields
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS pinterest_content (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_id INTEGER,
                
                -- Pinterest Form Fields --
                pinterest_title TEXT NOT NULL,           -- Title field
                pinterest_description TEXT NOT NULL,     -- Description field (includes hashtags)
                affiliate_link TEXT,                     -- Link field
                board_name TEXT DEFAULT 'Affiliate Products',  -- Board selection
                tagged_topics TEXT,                      -- Tagged topics (placeholder)
                
                -- Pexels Video Data --
                pexels_video_id INTEGER,                 -- Pexels video ID
                pexels_video_url TEXT,                   -- Pexels video page URL
                video_download_url TEXT,                 -- Direct video download URL
                video_preview_image TEXT,                -- Video thumbnail/preview
                video_duration INTEGER,                  -- Video duration in seconds
                video_width INTEGER,                     -- Video width
                video_height INTEGER,                    -- Video height
                video_search_query TEXT,                 -- Search query used to find video

                -- Research Data --
                research_summary TEXT,                   -- JSON research summary
                research_quality TEXT,                   -- Research quality level
                researched_benefits TEXT,                -- JSON array of researched benefits
                researched_features TEXT,                -- JSON array of researched features

                -- Content Metadata --
                marketing_angle TEXT,
                search_terms TEXT,                       -- JSON array of SEO terms
                topic_tags TEXT,                         -- JSON array of topic tags
                alt_text TEXT,
                generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                is_used BOOLEAN DEFAULT FALSE,
                posted_at TIMESTAMP,
                
                FOREIGN KEY (product_id) REFERENCES products (id)
            )
        ''')
        
        # Create indexes for performance
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_source_product_id ON products(source_product_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_processed ON products(processed_for_pinterest)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_content_used ON pinterest_content(is_used)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_pexels_video ON pinterest_content(pexels_video_id)')
        
        conn.commit()
        conn.close()
        logger.info("Enhanced Pinterest content database initialized")

    def _warm_up_ai_model(self):
        """Pre-warm the AI model to keep it loaded in memory for faster subsequent requests"""
        try:
            logger.info("🔥 Pre-warming local Qwen AI model for maximum speed...")
            warm_up_payload = {
                "model": self.ai_config.model,
                "prompt": "Ready",
                "stream": False,
                "keep_alive": "60m",
                "options": {
                    "num_predict": 1,
                    "temperature": 0.1
                }
            }

            response = requests.post(
                f"{self.ai_config.ollama_host}/api/generate",
                json=warm_up_payload,
                timeout=10
            )

            if response.status_code == 200:
                logger.info("✅ Local Qwen AI model pre-warmed and ready for ultra-fast generation!")
            else:
                logger.warning(f"⚠️ AI model warm-up failed: {response.status_code}")

        except Exception as e:
            logger.warning(f"⚠️ AI model warm-up failed: {e}")

    def check_ollama_status(self) -> bool:
        """Check if Ollama service is available with your Qwen model"""
        try:
            response = requests.get(f"{self.ai_config.ollama_host}/api/tags", timeout=5)
            if response.status_code == 200:
                models = response.json().get('models', [])
                model_names = [model.get('name', '') for model in models]

                if any(self.ai_config.model in name for name in model_names):
                    logger.info(f"✅ Ollama service available with model: {self.ai_config.model}")
                    return True
                else:
                    logger.warning(f"⚠️ Model {self.ai_config.model} not found in Ollama")
                    return False
            else:
                logger.warning(f"⚠️ Ollama service responded with status: {response.status_code}")
                return False

        except requests.exceptions.RequestException as e:
            logger.error(f"Ollama service not available: {e}")
            return False
        except Exception as e:
            logger.error(f"Error checking Ollama status: {e}")
            return False

    def _generate_with_ollama(self, prompt: str, system_prompt: str = "") -> Optional[str]:
        """Generate content using your local Qwen AI model via Ollama"""
        try:
            payload = {
                "model": self.ai_config.model,
                "prompt": prompt,
                "system": system_prompt,
                "stream": False,
                "keep_alive": "60m",  # Keep model loaded for 1 hour for maximum speed
                "options": {
                    "temperature": self.ai_config.temperature,
                    "num_predict": self.ai_config.max_tokens,
                    "top_k": 10,  # Further reduce sampling space for maximum speed
                    "top_p": 0.8,  # More focused sampling
                    "repeat_penalty": 1.05,  # Minimal repetition penalty for speed
                    "num_ctx": 1024,  # Minimal context window for maximum speed
                    "num_thread": 8  # Use multiple threads for faster processing
                }
            }

            response = requests.post(
                f"{self.ai_config.ollama_host}/api/generate",
                json=payload,
                timeout=60  # Generous timeout for AI generation
            )
            response.raise_for_status()

            result = response.json()
            generated_text = result.get('response', '').strip()

            if generated_text:
                logger.debug(f"Generated content length: {len(generated_text)} characters")
                return generated_text
            else:
                logger.warning("Empty response from Ollama")
                return None

        except requests.exceptions.RequestException as e:
            logger.error(f"Ollama API request failed: {e}")
            return None
        except Exception as e:
            logger.error(f"Error generating content with Ollama: {e}")
            return None

    def _filter_pinterest_content(self, content: str) -> str:
        """Filter content to ensure Pinterest compliance"""
        if not content:
            return content

        # Convert to lowercase for checking
        content_lower = content.lower()

        # Check for prohibited terms
        for term in self.ai_config.prohibited_terms:
            if term in content_lower:
                logger.warning(f"Filtered prohibited term: {term}")
                # Replace with neutral alternatives
                replacements = {
                    'commission': 'benefit',
                    'affiliate': 'recommended',
                    'affiliate link': 'product link',
                    'earn money': 'save money',
                    'make money': 'save money',
                    'referral': 'recommendation',
                    'promo code': 'special offer',
                    'discount code': 'special offer',
                    'cashback': 'savings',
                    'marketing': 'sharing',
                    'advertisement': 'recommendation',
                    'sponsored': 'featured',
                    'paid promotion': 'featured content',
                    'click here': 'learn more',
                    'buy now': 'discover',
                    'purchase': 'get',
                    'sale': 'offer',
                    'discount': 'savings',
                    'deal': 'offer',
                    'limited time': 'special',
                    'act now': 'discover',
                    'hurry': 'explore',
                    'urgent': 'important',
                    'exclusive offer': 'special offer'
                }

                replacement = replacements.get(term, 'amazing')
                content = content.replace(term, replacement)
                content = content.replace(term.title(), replacement.title())
                content = content.replace(term.upper(), replacement.upper())

        # Remove hashtags from middle of content (they should only be at the end)
        lines = content.split('\n')
        filtered_lines = []
        hashtag_section = False

        for line in lines:
            # If line starts with hashtags, we're in hashtag section
            if line.strip().startswith('#'):
                hashtag_section = True

            # If we're not in hashtag section and line contains hashtags in middle, remove them
            if not hashtag_section and '#' in line and not line.strip().startswith('#'):
                # Remove hashtags from middle of sentences
                import re
                line = re.sub(r'#\w+', '', line)
                line = ' '.join(line.split())  # Clean up extra spaces

            if line.strip():  # Only add non-empty lines
                filtered_lines.append(line)

        return '\n'.join(filtered_lines)

    def _web_search(self, query: str, num_results: int = 3, affiliate_link: str = None) -> List[Dict]:
        """Perform comprehensive web research using real web crawler with affiliate link"""
        try:
            logger.info(f"🔍 Comprehensive Web Research: {query}")
            if affiliate_link:
                logger.info(f"🔗 Using affiliate link: {affiliate_link}")

            # Use the real web crawler for comprehensive research
            try:
                import subprocess
                import json

                # Call the real web crawler with affiliate link
                cmd = ['python3', 'src/real_web_crawler.py', query]

                # Add affiliate link if provided
                if affiliate_link:
                    cmd.append(affiliate_link)

                result = subprocess.run(cmd, capture_output=True, text=True, timeout=60, cwd='.')

                if result.returncode == 0:
                    research_data = json.loads(result.stdout)
                    logger.info(f"✅ Comprehensive research: {research_data.get('crawled_sites', 0)} sites, {research_data.get('total_pages_crawled', 0)} pages")

                    # Convert research data to search results format
                    search_results = []
                    if research_data.get('search_results'):
                        for result_item in research_data['search_results']:
                            search_results.append({
                                'title': result_item.get('title', ''),
                                'url': result_item.get('url', ''),
                                'snippet': result_item.get('snippet', ''),
                                'research_data': research_data  # Include full research data
                            })
                    else:
                        # Create a single result with all research data
                        search_results.append({
                            'title': f'{query} - Comprehensive Research',
                            'url': 'https://research-complete.com',
                            'snippet': research_data.get('content_summary', ''),
                            'research_data': research_data
                        })

                    return search_results[:num_results]
                else:
                    logger.warning(f"Web crawler failed: {result.stderr}")

            except Exception as crawler_error:
                logger.warning(f"Real web crawler failed: {crawler_error}")

            # Fallback: Generate research based on product name analysis
            logger.info("Using enhanced fallback research")
            return self._generate_fallback_research(query)

        except Exception as e:
            logger.error(f"Web search failed: {e}")
            return self._generate_fallback_research(query)

    def _generate_fallback_research(self, query: str) -> List[Dict]:
        """Generate fallback research data based on product name analysis"""
        query_lower = query.lower()

        # Analyze product name for category and benefits
        research_data = {
            'title': f'{query} - Product Information',
            'snippet': '',
            'url': 'https://research-fallback.com'
        }

        # Health/Supplement products
        if any(word in query_lower for word in ['health', 'supplement', 'vitamin', 'nutrition', 'wellness']):
            research_data['snippet'] = f'{query} supports overall health and wellness. Benefits may include improved energy, better nutrition absorption, and enhanced vitality. Popular among health-conscious individuals seeking natural wellness solutions.'

        # Digital/Download products
        elif any(word in query_lower for word in ['guide', 'system', 'method', 'program', 'course']):
            research_data['snippet'] = f'{query} is a comprehensive digital program designed to help users achieve their goals. Features step-by-step guidance, practical techniques, and proven strategies for success.'

        # Manifestation/Self-help products
        elif any(word in query_lower for word in ['manifestation', 'wealth', 'success', 'mindset', 'abundance']):
            research_data['snippet'] = f'{query} focuses on personal development and mindset transformation. Helps users develop positive thinking patterns, goal achievement strategies, and success-oriented habits.'

        # Technology/Software products
        elif any(word in query_lower for word in ['software', 'app', 'tool', 'system', 'technology']):
            research_data['snippet'] = f'{query} is a technology solution designed to improve productivity and efficiency. Features user-friendly interface, advanced capabilities, and practical applications for everyday use.'

        # General fallback
        else:
            research_data['snippet'] = f'{query} is designed to provide value and benefits to users. Features practical applications, user-friendly design, and proven effectiveness for achieving desired outcomes.'

        return [research_data]

    def _fetch_product_page(self, url: str) -> Optional[str]:
        """Fetch product page content using web-fetch equivalent"""
        try:
            logger.debug(f"📄 Fetching product page: {url}")

            # Use requests to fetch the page (simulating web-fetch)
            response = requests.get(url, timeout=10, headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            })

            if response.status_code == 200:
                content = response.text
                # Extract meaningful content (basic implementation)
                # Remove HTML tags and get text content
                import re
                text_content = re.sub(r'<[^>]+>', ' ', content)
                text_content = ' '.join(text_content.split())

                # Limit content length
                if len(text_content) > 2000:
                    text_content = text_content[:2000] + "..."

                logger.debug(f"✅ Fetched {len(text_content)} characters of content")
                return text_content
            else:
                logger.warning(f"Failed to fetch page: {response.status_code}")
                return None

        except Exception as e:
            logger.error(f"Page fetch failed: {e}")
            return None

    def _research_product(self, product_title: str, affiliate_link: str = None) -> Dict:
        """Research product using comprehensive web crawling"""
        try:
            logger.info(f"🔬 Starting comprehensive product research for: {product_title}")

            # Clean product title for search
            search_query = self._clean_title_for_pinterest(product_title)

            # Perform comprehensive web search and crawling with affiliate link
            search_results = self._web_search(search_query, num_results=3, affiliate_link=affiliate_link)

            if not search_results:
                logger.warning("No search results found")
                return {'research_quality': 'none'}

            # Extract comprehensive research data
            research_data = None
            for result in search_results:
                if result.get('research_data'):
                    research_data = result['research_data']
                    break

            if research_data:
                # Use comprehensive research data from web crawler
                benefits = research_data.get('key_benefits', [])
                features = research_data.get('main_features', [])
                content = research_data.get('content_summary', '')
                quality = research_data.get('research_quality', 'basic')

                # Map research quality levels
                if quality == 'comprehensive':
                    quality = 'detailed'
                elif quality == 'fallback':
                    quality = 'basic'

                final_research = {
                    'product_name': search_query,
                    'search_results': search_results,
                    'key_benefits': benefits[:10],  # Top 10 benefits
                    'main_features': features[:8],   # Top 8 features
                    'research_quality': quality,
                    'page_content': content[:2000],  # Limit content length
                    'crawled_sites': research_data.get('crawled_sites', 0),
                    'total_pages': research_data.get('total_pages_crawled', 0),
                    'target_audience': '',
                    'product_type': ''
                }

                logger.info(f"✅ Comprehensive research: {quality} quality, {len(benefits)} benefits, {len(features)} features, {research_data.get('crawled_sites', 0)} sites crawled")

                return final_research

        except Exception as e:
            logger.error(f"Product research failed: {e}")
            return {'summary': 'Research failed', 'details': str(e)}

    def _extract_benefits_from_text(self, text: str) -> List[str]:
        """Extract potential benefits from research text"""
        benefits = []

        # Simple benefit extraction patterns
        benefit_patterns = [
            r'helps?\s+(?:you\s+)?([^.!?]+)',
            r'improves?\s+([^.!?]+)',
            r'increases?\s+([^.!?]+)',
            r'reduces?\s+([^.!?]+)',
            r'enhances?\s+([^.!?]+)',
            r'boosts?\s+([^.!?]+)'
        ]

        for pattern in benefit_patterns:
            matches = re.findall(pattern, text.lower())
            for match in matches[:2]:  # Limit to 2 per pattern
                clean_benefit = match.strip()[:50]  # Limit length
                if len(clean_benefit) > 10:
                    benefits.append(clean_benefit)

        return benefits[:5]  # Return top 5 benefits

    def _extract_features_from_text(self, text: str) -> List[str]:
        """Extract potential features from research text"""
        features = []

        # Simple feature extraction patterns
        feature_patterns = [
            r'includes?\s+([^.!?]+)',
            r'contains?\s+([^.!?]+)',
            r'provides?\s+([^.!?]+)',
            r'offers?\s+([^.!?]+)',
            r'features?\s+([^.!?]+)'
        ]

        for pattern in feature_patterns:
            matches = re.findall(pattern, text.lower())
            for match in matches[:2]:  # Limit to 2 per pattern
                clean_feature = match.strip()[:50]  # Limit length
                if len(clean_feature) > 10:
                    features.append(clean_feature)

        return features[:5]  # Return top 5 features
    
    async def search_pexels_video(self, search_query: str, orientation: str = "portrait") -> Optional[Dict]:
        """Search for relevant video on Pexels API"""
        if not self.pexels_api_key:
            logger.warning("Pexels API key not available, skipping video search")
            return None
        
        try:
            headers = {
                'Authorization': self.pexels_api_key
            }
            
            params = {
                'query': search_query,
                'orientation': orientation,  # portrait for Pinterest
                'size': 'medium',  # Full HD
                'per_page': 10  # Get more options for AI to choose from
            }
            
            url = 'https://api.pexels.com/videos/search'
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        videos = data.get('videos', [])
                        
                        if videos:
                            # Select the first video
                            video = videos[0]
                            
                            # Find the best quality video file
                            video_files = video.get('video_files', [])
                            best_video = None
                            
                            # Prefer HD quality, portrait orientation
                            for vf in video_files:
                                if vf.get('quality') == 'hd' and vf.get('width', 0) < vf.get('height', 0):
                                    best_video = vf
                                    break
                            
                            # Fallback to any HD video
                            if not best_video:
                                for vf in video_files:
                                    if vf.get('quality') == 'hd':
                                        best_video = vf
                                        break
                            
                            # Fallback to first video
                            if not best_video and video_files:
                                best_video = video_files[0]
                            
                            if best_video:
                                return {
                                    'pexels_video_id': video.get('id'),
                                    'pexels_video_url': video.get('url'),
                                    'video_download_url': best_video.get('link'),
                                    'video_preview_image': video.get('image'),
                                    'video_duration': video.get('duration'),
                                    'video_width': best_video.get('width'),
                                    'video_height': best_video.get('height'),
                                    'video_search_query': search_query
                                }
                        
                        logger.info(f"No videos found for query: {search_query}")
                        return None
                    
                    else:
                        logger.error(f"Pexels API error: {response.status}")
                        return None
                        
        except Exception as e:
            logger.error(f"Error searching Pexels videos: {e}")
            return None

    async def ai_select_best_video(self, search_query: str, product_title: str, pinterest_title: str = None, research_data: Dict = None, orientation: str = "portrait") -> Optional[Dict]:
        """Use AI to search and select the best video from multiple Pexels options"""
        if not self.pexels_api_key:
            logger.warning("Pexels API key not available, skipping AI video selection")
            return None

        try:
            headers = {
                'Authorization': self.pexels_api_key
            }

            params = {
                'query': search_query,
                'orientation': orientation,  # portrait for Pinterest
                'size': 'medium',  # Full HD
                'per_page': 10  # Get multiple options for AI to choose from
            }

            url = 'https://api.pexels.com/videos/search'

            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        videos = data.get('videos', [])

                        if videos:
                            # Use AI to select the best video
                            selected_video = self._ai_choose_best_video(videos, product_title, pinterest_title, research_data)

                            if selected_video:
                                # Find the best quality video file for the selected video
                                video_files = selected_video.get('video_files', [])
                                best_video = None

                                # Prefer HD quality, portrait orientation
                                for vf in video_files:
                                    if vf.get('quality') == 'hd' and vf.get('width', 0) < vf.get('height', 0):
                                        best_video = vf
                                        break

                                # Fallback to any HD video
                                if not best_video:
                                    for vf in video_files:
                                        if vf.get('quality') == 'hd':
                                            best_video = vf
                                            break

                                # Fallback to first video
                                if not best_video and video_files:
                                    best_video = video_files[0]

                                if best_video:
                                    return {
                                        'pexels_video_id': selected_video.get('id'),
                                        'pexels_video_url': selected_video.get('url'),
                                        'video_download_url': best_video.get('link'),
                                        'video_preview_image': selected_video.get('image'),
                                        'video_duration': selected_video.get('duration'),
                                        'video_width': best_video.get('width'),
                                        'video_height': best_video.get('height'),
                                        'video_search_query': search_query
                                    }

                        logger.info(f"No videos found for AI selection with query: {search_query}")
                        return None

                    else:
                        logger.error(f"Pexels API error: {response.status}")
                        return None

        except Exception as e:
            logger.error(f"Error in AI video selection: {e}")
            return None

    def _ai_choose_best_video(self, videos: List[Dict], product_title: str, pinterest_title: str = None, research_data: Dict = None) -> Optional[Dict]:
        """Use AI to analyze and choose the best video from multiple options"""

        # Check if Ollama is available
        if not self.check_ollama_status():
            logger.warning("Ollama not available for video selection, using first video")
            return videos[0] if videos else None

        # Prepare video options for AI analysis
        video_options = []
        for i, video in enumerate(videos[:5]):  # Analyze top 5 videos
            # Get more descriptive information about the video
            tags = video.get('tags', [])
            video_files = video.get('video_files', [])

            # Try to get the most descriptive information available
            description_parts = []
            if tags:
                description_parts.append(f"Tags: {', '.join(tags[:3])}")

            # Get video URL for reference (though AI won't see the actual video)
            video_url = video.get('url', '')
            if 'pexels.com/video/' in video_url:
                # Extract descriptive part from URL
                url_part = video_url.split('/')[-2] if '/' in video_url else ''
                if url_part and url_part != 'video':
                    description_parts.append(f"Theme: {url_part.replace('-', ' ')}")

            video_info = {
                'option': i + 1,
                'duration': video.get('duration', 0),
                'description': '; '.join(description_parts) if description_parts else 'No description available',
                'width': video.get('width', 0),
                'height': video.get('height', 0),
                'orientation': 'Portrait' if video.get('height', 0) > video.get('width', 0) else 'Landscape'
            }
            video_options.append(video_info)

        # Prepare research context
        research_context = ""
        if research_data:
            if research_data.get('key_benefits'):
                research_context += f"\nRESEARCHED BENEFITS: {', '.join(research_data['key_benefits'][:3])}"
            if research_data.get('key_features'):
                research_context += f"\nKEY FEATURES: {', '.join(research_data['key_features'][:2])}"

        # Create AI prompt for video selection
        system_prompt = """You are a Pinterest video expert who selects the perfect video for affiliate marketing pins. Choose the video that will get the most engagement and clicks from the target audience.

CRITICAL VIDEO SELECTION CRITERIA:
- PINTEREST AESTHETICS: Bright, clean, professional, aspirational
- TARGET AUDIENCE APPEAL: Will this resonate with people who would buy this product?
- ENGAGEMENT POTENTIAL: Will this make people stop scrolling and click?
- VISUAL QUALITY: Professional, high-quality, Pinterest-worthy
- RELEVANCE: Matches the product theme and buyer psychology
- DURATION: Prefer 10-30 seconds for Pinterest (not too long)

PINTEREST SUCCESS FACTORS:
- Beautiful, aspirational visuals
- Clear, professional quality
- Matches the lifestyle/interests of target buyers
- Creates curiosity and desire to learn more
- Fits Pinterest's aesthetic standards"""

        video_descriptions = "\n".join([
            f"Option {v['option']}: Duration: {v['duration']}s, {v['description']}, {v['orientation']}"
            for v in video_options
        ])

        prompt = f"""Find the ONE video that PERFECTLY matches: "{product_title}"
{research_context}

VIDEO OPTIONS:
{video_descriptions}

MATCHING RULES - BE VERY SPECIFIC:
- BRAIN/COGNITIVE products (theta waves, genius, focus) → brain, neurons, scientific, thinking, meditation videos
- MUSCLE/FITNESS products (amino, strength, endurance) → gym, workout, muscle building, fitness videos
- SPIRITUAL/PINEAL products (consciousness, awakening) → meditation, spiritual, peaceful, mindfulness videos
- WEALTH/MANIFESTATION products (money, abundance) → success, luxury, visualization, achievement videos
- DIY/BUILDING products (generator, construction) → building, tools, workshop, construction videos
- HEALTH/WELLNESS products (supplements, nutrition) → healthy lifestyle, nutrition, wellness videos

CRITICAL: Pick the video whose content/theme DIRECTLY relates to what this product does. Ignore generic lifestyle content.

Which option number (1-5) has content that directly matches this product's function?"""

        try:
            response = self._generate_with_ollama(system_prompt, prompt)
            if response:
                # Extract number from response (handle verbose responses)
                response_clean = response.strip()

                # Try to find a number in the response
                import re
                numbers = re.findall(r'\b([1-5])\b', response_clean)

                if numbers:
                    selected_option = int(numbers[0])  # Use first number found
                    if 1 <= selected_option <= len(videos):
                        selected_video = videos[selected_option - 1]
                        logger.info(f"🎬 AI selected video option {selected_option} for {product_title}")
                        return selected_video
                    else:
                        logger.warning(f"AI selected invalid option {selected_option}, using first video")
                        return videos[0]
                elif response_clean.isdigit():
                    # Direct number response
                    selected_option = int(response_clean)
                    if 1 <= selected_option <= len(videos):
                        selected_video = videos[selected_option - 1]
                        logger.info(f"🎬 AI selected video option {selected_option} for {product_title}")
                        return selected_video
                    else:
                        logger.warning(f"AI selected invalid option {selected_option}, using first video")
                        return videos[0]
                else:
                    logger.warning(f"AI gave invalid video selection response: {response_clean[:100]}..., using first video")
                    return videos[0]
            else:
                logger.warning("AI gave empty video selection response, using first video")
                return videos[0]

        except Exception as e:
            logger.error(f"Error in AI video selection: {e}")
            return videos[0] if videos else None

    def generate_ai_video_search_query(self, product_title: str, category: str = None, research_data: Dict = None, pinterest_title: str = None) -> str:
        """Use AI to generate intelligent video search query based on product research and Pinterest content"""

        # Use the smaller, better AI model like your other successful scripts
        if not self.check_ollama_status():
            logger.warning("Ollama not available, using fallback video search query generation")
            return self._generate_fallback_video_query(product_title, category)

        # Use the same approach as your successful AI scripts with qwen2.5:0.5b
        try:
            # Extract key benefits from research data
            benefits_text = ""
            if research_data and research_data.get('key_benefits'):
                benefits_text = ' '.join(research_data['key_benefits'][:2])

            # Simple, direct prompt like your other successful AI scripts
            prompt = f"""Generate 2-3 video search words for: {product_title}
Benefits: {benefits_text}

Examples:
Brain product → brain neurons
Fitness product → gym workout
Manifestation → success luxury
Spiritual → meditation peaceful

Just the search words:"""

            # Use qwen2.5:0.5b model like your other scripts
            payload = {
                "model": "qwen2.5:0.5b",  # Same model as your successful scripts
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": 0.3,  # Lower temperature for more focused output
                    "num_predict": 20,   # Very short response
                    "top_k": 5,
                    "top_p": 0.8
                }
            }

            response = requests.post(
                "http://localhost:11434/api/generate",
                json=payload,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                search_terms = result.get('response', '').strip()

                # Clean up the response
                words = search_terms.split()
                clean_words = [w for w in words if w.isalpha() and len(w) > 2]

                if 2 <= len(clean_words) <= 4:
                    search_query = ' '.join(clean_words[:3])
                    logger.info(f"🎬 AI generated video search query for {product_title}: {search_query}")
                    return search_query

            # Fallback if AI fails
            return self._generate_fallback_video_query(product_title, category)

        except Exception as e:
            logger.error(f"Error with AI video search query: {e}")
            return self._generate_fallback_video_query(product_title, category)

        # Prepare research context
        research_context = ""
        if research_data:
            if research_data.get('key_benefits'):
                research_context += f"\nRESEARCHED BENEFITS: {', '.join(research_data['key_benefits'][:3])}"
            if research_data.get('key_features'):
                research_context += f"\nKEY FEATURES: {', '.join(research_data['key_features'][:2])}"

        # Create AI prompt for video search query generation
        system_prompt = """You are a Pinterest video expert who creates perfect video search queries for Pexels API. Generate search terms that will find videos that perfectly match Pinterest aesthetics and target audiences.

CRITICAL VIDEO SEARCH RULES:
- Generate 2-4 search terms that will find Pinterest-perfect videos
- Focus on VISUAL AESTHETICS that Pinterest users love
- Consider the TARGET AUDIENCE who would buy this product
- Think about LIFESTYLE and ASPIRATIONAL content
- Avoid literal product searches - focus on the FEELING and LIFESTYLE
- Use terms that find beautiful, engaging, Pinterest-style videos
- Consider lighting, mood, and visual appeal

PINTEREST VIDEO AESTHETICS:
- Bright, clean, aspirational visuals
- Lifestyle and wellness themes
- Beautiful people, nature, success imagery
- Motivational and inspiring content
- High-quality, professional-looking videos"""

        prompt = f"""Product: "{product_title}"{research_context}

Based on the product benefits, write ONLY 2-3 search words:

Examples:
- Brain enhancement → brain neurons scientific
- Muscle supplement → muscle building gym
- Manifestation → success visualization luxury
- Pineal gland → meditation spiritual awakening
- DIY generator → diy construction workshop

Your 2-3 words:"""

        try:
            response = self._generate_with_ollama(system_prompt, prompt)
            if response and len(response.strip()) > 0:
                # Clean and validate the response - extract only the search terms
                search_query = response.strip().lower()

                # Extract clean search terms from AI response
                # Remove common prefixes and clean up
                search_query = search_query.replace('your 2-3 words:', '').replace('search terms:', '').strip()

                # Split into lines and find the best one
                lines = search_query.split('\n')
                for line in lines:
                    line = line.strip()
                    # Skip empty lines and explanatory text
                    if not line or any(skip_word in line.lower() for skip_word in ['product', 'based on', 'examples', 'your']):
                        continue
                    # Clean the line
                    words = line.replace('"', '').replace("'", '').replace(':', '').replace('->', '').split()
                    # Look for 2-4 alphabetic words
                    if 2 <= len(words) <= 4 and all(len(word) > 1 and word.replace('-', '').isalpha() for word in words):
                        search_query = ' '.join(words)
                        break
                else:
                    # Try to extract from the full response
                    words = search_query.replace('"', '').replace("'", '').replace(':', '').split()
                    # Find the last sequence of 2-3 reasonable words (likely the answer)
                    for i in range(len(words) - 2, -1, -1):
                        candidate_words = words[i:i+3]
                        if 2 <= len(candidate_words) <= 3 and all(len(w) > 1 and w.replace('-', '').isalpha() for w in candidate_words):
                            search_query = ' '.join(candidate_words)
                            break
                    else:
                        logger.warning(f"Could not extract clean search terms from: {response[:100]}")
                        return self._generate_fallback_video_query(product_title, category)

                logger.info(f"🎬 AI generated video search query for {product_title}: {search_query}")
                return search_query
            else:
                logger.warning("AI generated empty video search query, using fallback")
                return self._generate_fallback_video_query(product_title, category)

        except Exception as e:
            logger.error(f"Error generating AI video search query: {e}")
            return self._generate_fallback_video_query(product_title, category)

    def _generate_fallback_video_query(self, product_title: str, category: str = None) -> str:
        """Fallback video search query generation when AI is not available"""
        clean_title = product_title.lower()

        # Specific keyword-based fallback matching actual product functions
        if any(word in clean_title for word in ['brain', 'genius', 'mind', 'focus', 'cognitive', 'theta', 'wave']):
            return "brain neurons scientific"
        elif any(word in clean_title for word in ['amino', 'muscle', 'strength', 'fitness', 'workout', 'protein']):
            return "muscle building gym"
        elif any(word in clean_title for word in ['pineal', 'spiritual', 'consciousness', 'awakening']):
            return "meditation spiritual awakening"
        elif any(word in clean_title for word in ['wealth', 'money', 'manifestation', 'midas', 'abundance']):
            return "success visualization luxury"
        elif any(word in clean_title for word in ['generator', 'diy', 'build', 'construction', 'edison']):
            return "diy construction workshop"
        elif any(word in clean_title for word in ['health', 'supplement', 'wellness', 'nitric', 'oxide']):
            return "healthy lifestyle nutrition"
        elif any(word in clean_title for word in ['garden', 'medicinal', 'herbs', 'plants']):
            return "garden herbs natural"
        elif any(word in clean_title for word in ['doctor', 'medical', 'home', 'treatment']):
            return "medical health care"
        else:
            return "wellness transformation lifestyle"
    
    def generate_pinterest_title(self, clean_title: str, category: str = None, research_data: Dict = None) -> str:
        """Generate Pinterest-compliant title using local Qwen AI model with research data"""

        # Check if Ollama is available
        if not self.check_ollama_status():
            logger.warning("Ollama not available, using fallback title generation")
            return self._generate_fallback_title(clean_title, category)

        # Create AI prompt for Pinterest title with research data
        system_prompt = """You are a Pinterest content expert who creates ACCURATE titles using ONLY the provided research data. NEVER make up or guess product details.

🚨 CRITICAL ACCURACY RULES - NEVER VIOLATE:
- USE ONLY information from the provided research data
- NEVER invent, guess, or hallucinate product details
- If research says "7-second", write "7-second" - NOT "4-minute" or any other duration
- If research mentions specific ingredients, use THOSE ingredients exactly
- If research mentions specific methods, use THOSE methods exactly
- NEVER substitute or change any product specifications

TITLE RULES:
- NEVER use: commission, affiliate, earn money, make money, referral, promo code, discount, sale, buy now, click here, limited time, hurry, urgent
- NEVER mention pricing, discounts, or sales
- CREATE UNIQUE TITLES using research data
- Use emojis sparingly (0-1 per title)
- Maximum 70 characters
- Focus on what THIS SPECIFIC product actually DOES or IS from research
- Use the ACTUAL product name if extracted

🚨 USE RESEARCH DATA EXACTLY AS PROVIDED:
- Extract specific details from research data only
- Use exact terminology and specifications from research
- Create titles based on actual product information found in research

RESEARCH DATA HANDLING:
- Use actual product details from research to create specific, accurate titles
- Focus on the real benefits and features found in the research
- If research mentions specific techniques, ingredients, or methods - use them!
- Make titles product-specific, not generic"""

        # Build prompt with research data and actual product name
        research_context = ""
        actual_product_name = clean_title  # Default fallback

        if research_data:
            # Use extracted actual product name if available
            if research_data.get('actual_product_name'):
                actual_product_name = research_data['actual_product_name']
                research_context += f"\nACTUAL PRODUCT NAME: {actual_product_name}"

            if research_data.get('key_benefits'):
                benefits = research_data['key_benefits'][:3]  # Top 3 benefits
                research_context += f"\nProduct Benefits: {', '.join(benefits)}"

            if research_data.get('main_features'):
                features = research_data['main_features'][:2]  # Top 2 features
                research_context += f"\nKey Features: {', '.join(features)}"

        prompt = f"""Create a specific Pinterest-compliant title for: "{actual_product_name}"
        Original Title: "{clean_title}"
        Category: {category or 'lifestyle'}{research_context}

        REQUIREMENTS:
        - Maximum 70 characters total
        - Include product name: {actual_product_name}
        - Use ACTUAL details from research
        - Use 0-1 emojis maximum
        - NO explanations, notes, or comments
        - NO quotes around the title

        EXAMPLES:
        - Prostadine: Natural Prostate Support 🌿
        - Genius Wave: 7-Second Brain Activation
        - Pineal Guardian: Memory & Focus Boost

        Return ONLY the title, nothing else:"""

        ai_title = self._generate_with_ollama(prompt, system_prompt)

        logger.info(f"🤖 Raw AI title generated: {ai_title}")

        if ai_title:
            # Extract just the title (remove any explanatory text)
            clean_ai_title = self._extract_title_from_ai_response(ai_title.strip())
            logger.info(f"🔍 Extracted title: {clean_ai_title}")

            # Filter the generated title
            filtered_title = self._filter_pinterest_content(clean_ai_title)
            logger.info(f"🔍 Filtered title: {filtered_title}")
            logger.info(f"📏 Title length: {len(filtered_title) if filtered_title else 0}")

            if filtered_title and len(filtered_title) <= 100:
                logger.info(f"✅ Using AI generated title: {filtered_title}")
                return filtered_title
            elif filtered_title:
                logger.warning(f"⚠️ Title too long ({len(filtered_title)} chars): {filtered_title}")
            else:
                logger.warning("⚠️ Title was completely filtered out")

        logger.warning("AI title generation failed, using fallback")
        return self._generate_fallback_title(clean_title, category)

    def _extract_title_from_ai_response(self, ai_response: str) -> str:
        """Extract just the title from AI response, removing explanatory text"""
        if not ai_response:
            return ""

        # Split by lines and take the first non-empty line
        lines = [line.strip() for line in ai_response.split('\n') if line.strip()]
        if not lines:
            return ""

        # Take the first line as the title
        title = lines[0]

        # Remove quotes if present
        title = title.strip('"\'')

        # Remove common prefixes
        prefixes_to_remove = ['Title:', 'Pinterest Title:', 'Generated Title:']
        for prefix in prefixes_to_remove:
            if title.startswith(prefix):
                title = title[len(prefix):].strip()

        # If there's a parenthetical note, remove it
        if '(' in title:
            title = title.split('(')[0].strip()

        return title

    def _extract_description_from_ai_response(self, ai_response: str) -> str:
        """Extract just the description content from AI response, removing chatter and hashtags"""
        if not ai_response:
            return ""

        # Remove common AI chatter patterns
        chatter_patterns = [
            r"Based on.*?I've generated.*?:",
            r"Here's.*?description.*?:",
            r"I've created.*?description.*?:",
            r"Based on.*?requirements.*?:",
            r"Here are.*?hashtags.*?:",
            r"I've generated.*?hashtags.*?:",
            r"Based on.*?benefits.*?:",
        ]

        clean_content = ai_response
        for pattern in chatter_patterns:
            clean_content = re.sub(pattern, "", clean_content, flags=re.IGNORECASE | re.DOTALL)

        # Split by lines and remove hashtag lines
        lines = []
        for line in clean_content.split('\n'):
            line = line.strip()
            if line and not line.startswith('#') and '#' not in line:
                lines.append(line)
            elif line and '#' in line:
                # If line has hashtags, take only the part before hashtags
                before_hashtag = line.split('#')[0].strip()
                if before_hashtag:
                    lines.append(before_hashtag)
                break  # Stop processing after hitting hashtags

        # Join the clean lines
        description = '\n'.join(lines).strip()

        # Remove any remaining chatter at the end
        end_chatter_patterns = [
            r"Ready to experience.*?$",
            r"Curious to learn.*?$",
            r"Want to discover.*?$",
        ]

        for pattern in end_chatter_patterns:
            if re.search(pattern, description, re.IGNORECASE):
                # Keep the call-to-action as it's part of the content
                break

        return description

    def _extract_hashtags_from_ai_response(self, ai_response: str) -> str:
        """Extract just the hashtags from AI response, removing chatter"""
        if not ai_response:
            return ""

        # Find all hashtags in the response
        hashtags = re.findall(r'#\w+', ai_response)

        # If no hashtags found, return empty
        if not hashtags:
            return ""

        # Join hashtags with spaces
        return ' '.join(hashtags)

    def _generate_fallback_title(self, clean_title: str, category: str = None) -> str:
        """Fallback title generation if AI is unavailable"""
        if len(clean_title) <= 80:
            return f"✨ {clean_title} - Transform Your Life!"
        else:
            # Truncate if too long
            short_title = clean_title[:60] + "..."
            return f"✨ {short_title} - Life Changing!"
    
    def generate_pinterest_description_with_hashtags(self, clean_title: str, category: str, affiliate_link: str, research_data: Dict = None) -> str:
        """Generate Pinterest description with hashtags using local Qwen AI model and research data"""

        # Check if Ollama is available
        if not self.check_ollama_status():
            logger.warning("Ollama not available, using fallback description generation")
            return self._generate_fallback_description(clean_title, category)

        # Create AI prompt for Pinterest description with research data
        system_prompt = """Write a Pinterest description using ONLY the research data provided. Be accurate and specific.

RULES:
- Use ONLY the research data provided below
- If research mentions "7-second", write "7-second" (not "30-minute")
- If research mentions "digital audio track", write "digital audio track"
- Write 250-350 characters total
- Add 3 bullet points with ✅
- End with "Ready to experience the difference?"
- Use 1 emoji
- No hashtags

Write a Pinterest description:"""

        # Build comprehensive research context for detailed descriptions
        research_context = ""
        actual_product_name = clean_title  # Default fallback

        if research_data:
            # Use extracted actual product name if available
            if research_data.get('actual_product_name'):
                actual_product_name = research_data['actual_product_name']
                research_context += f"\nACTUAL PRODUCT NAME: {actual_product_name}"

            if research_data.get('key_benefits'):
                benefits = research_data['key_benefits'][:6]  # Top 6 benefits
                research_context += f"\nRESEARCHED BENEFITS: {', '.join(benefits)}"

            if research_data.get('main_features'):
                features = research_data['main_features'][:4]  # Top 4 features
                research_context += f"\nKEY FEATURES: {', '.join(features)}"

            if research_data.get('page_content'):
                content_snippet = research_data['page_content'][:500]  # First 500 chars
                research_context += f"\nPRODUCT DETAILS: {content_snippet}"

            if research_data.get('research_quality') == 'comprehensive':
                research_context += "\nCOMPREHENSIVE RESEARCH: Use specific product details, ingredients, methods, and techniques from the research"

        # Debug logging to see what research context is being passed
        logger.info(f"🔍 Research context for {actual_product_name}: {research_context[:200]}...")

        prompt = f"""Create a detailed Pinterest-compliant description for: "{actual_product_name}"
        Original Title: "{clean_title}"
        Category: {category or 'lifestyle'}{research_context}

        REQUIRED STRUCTURE:
        1. SPECIFIC OPENING: What this product actually IS (not generic transformation)
        2. PRODUCT DETAILS: Specific ingredients, methods, duration, or techniques from research
        3. BULLET POINTS (✅): 3-4 specific benefits based on actual research data (NO REPETITION)
        4. ENGAGING CLOSING: Call-to-action that creates curiosity

        ANTI-REPETITION RULES:
        - Each sentence must be unique and different
        - Do not repeat the same information in different words
        - Vary your language and avoid redundant phrases
        - If you mention "7 seconds" once, don't repeat it again

        NO MARKETING JARGON:
        - NO sales language: "60-day money back guarantee", "limited time", "special offer"
        - NO advertising terms: "act now", "don't miss out", "exclusive access"
        - Focus on what the product IS and DOES, not sales pitches
        - This is informational content, not an advertisement

        SPECIFIC REQUIREMENTS:
        - TARGET: 300-400 characters total (Pinterest optimal length)
        - Use ACTUAL product details from research (ingredients, methods, techniques)
        - NO generic phrases like "transform your life" or "unlock potential"
        - NO repetitive content - each sentence must be unique
        - Be SPECIFIC about what the product does and contains
        - Use 1-2 emojis maximum (Pinterest best practice)
        - NO hashtags in description text (hashtags added separately)
        - End with engaging call-to-action like "Ready to experience the difference?" or "Curious to learn the method?"

        🚨 CRITICAL: USE ONLY RESEARCH DATA PROVIDED ABOVE
        - Extract specific details from the research data only
        - Use exact terminology from research (don't change words)
        - Use exact durations from research (don't invent new ones)
        - Use exact ingredients/methods from research (don't substitute)
        - If research data is limited, keep description general but accurate

        Generate ONLY the description using research data:"""

        # Debug logging to see what research context is being passed
        logger.info(f"🔍 Research context for {clean_title}: {research_context[:300]}...")

        ai_description = self._generate_with_ollama(prompt, system_prompt)

        # Debug logging to see what AI generated
        logger.info(f"🤖 AI generated description for {clean_title}: {ai_description[:150]}...")

        if ai_description:
            # Extract just the description content (remove AI chatter)
            clean_description = self._extract_description_from_ai_response(ai_description.strip())
            logger.info(f"🔍 Extracted clean description: {clean_description[:100]}...")

            # Filter the description
            filtered_description = self._filter_pinterest_content(clean_description)

            # Generate hashtags separately with research context
            hashtags = self._generate_ai_hashtags(category, clean_title, research_data)

            # Combine description and hashtags properly
            if filtered_description:
                # Add hashtags at the end
                complete_description = f"{filtered_description}\n\n{hashtags}"
                return complete_description

        logger.warning("AI description generation failed, using fallback")
        return self._generate_fallback_description(clean_title, category)

    def _generate_fallback_description(self, clean_title: str, category: str) -> str:
        """Fallback description generation if AI is unavailable"""
        category_text = category.lower() if category else "amazing product"
        hashtags = self._generate_hashtags(category, clean_title)

        description = f"""🚀 Discover {clean_title}!

Transform your life with this incredible {category_text}. Join thousands who have already experienced amazing results!

✅ Proven results
✅ Easy to use
✅ Life-changing benefits

Ready to start your transformation? Click the link to learn more!

{hashtags}"""

        return description
    
    def _generate_ai_hashtags(self, category: str, title: str, research_data: Dict = None) -> str:
        """Generate relevant hashtags using local Qwen AI model with research data"""

        # Check if Ollama is available
        if not self.check_ollama_status():
            return self._generate_hashtags(category, title)

        # Create AI prompt for hashtag generation with research data
        system_prompt = """You are a Pinterest hashtag expert. Create exactly 4 hashtags that target BUYER COMMUNITIES and INTERESTS, not product names.

CRITICAL HASHTAG RULES - FOLLOW EXACTLY:
- Generate EXACTLY 4 hashtags (Pinterest best practice: 2-5)
- NEVER use: #affiliate, #commission, #earnmoney, #makemoney, #referral, #promocode, #discount, #sale, #buynow, #deal
- ABSOLUTELY NO PRODUCT NAMES: Never use #GeniusWave, #Pineal10x, #PinealXT, #MidasManifestationSystem, #LostGenerator, #WealthSignal, #CircO2, #AdvancedAmino, etc.
- NEVER use any words from the product title in hashtags
- TARGET BUYER COMMUNITIES: Who would buy this? What are their interests?
- Focus on lifestyle, hobbies, problems they're solving, communities they belong to
- Use searchable keywords that buyers actually search for
- Mix popular community hashtags with niche interest hashtags

PRODUCT NAME BLACKLIST - NEVER USE THESE IN HASHTAGS:
- Any variation of the product name
- Any words that appear in the product title
- Any brand names or product-specific terms
- Target the right audience
- Help with organic discoverability
- Use research data to create more accurate hashtags"""

        # Build research context for hashtags
        research_context = ""
        if research_data:
            if research_data.get('key_benefits'):
                benefits = research_data['key_benefits'][:3]
                research_context += f"\nProduct Benefits: {', '.join(benefits)}"

            if research_data.get('main_features'):
                features = research_data['main_features'][:2]
                research_context += f"\nKey Features: {', '.join(features)}"

        prompt = f"""Generate exactly 4 Pinterest hashtags targeting BUYER COMMUNITIES for: "{title}"
        Category: {category or 'lifestyle'}{research_context}

        REQUIREMENTS:
        - Generate EXACTLY 4 hashtags (Pinterest best practice: 2-5)
        - TARGET BUYER COMMUNITIES, not product names
        - Think: Who would buy this? What communities do they belong to?
        - NO sales/affiliate hashtags
        - NEVER use product names in hashtags (no "GeniusWave", "Pineal10x", "PinealXT", etc.)
        - NEVER use words from the product title in hashtags
        - Focus on buyer interests, lifestyles, problems they're solving
        - Format: #hashtag (no spaces), separated by spaces

        THINK THROUGH BUYER COMMUNITIES FOR ANY PRODUCT:
        1. WHO would buy this? (demographics, interests, lifestyle)
        2. WHAT PROBLEMS does it solve? (hashtags around the problem)
        3. WHAT COMMUNITIES do they belong to? (hobbies, interests, lifestyles)
        4. WHAT DO THEY SEARCH FOR? (popular keywords in their space)

        GENERAL HASHTAG APPROACH:
        - Problem-solving hashtags: What issue does this address?
        - Lifestyle hashtags: What lifestyle does the buyer have?
        - Interest hashtags: What are their hobbies/passions?
        - Community hashtags: What groups do they belong to?

        EXAMPLES OF THINKING PROCESS (adapt to your specific product):
        - Tech gadget → #TechLovers #GadgetGeeks #SmartHome #Innovation
        - Fashion item → #StyleInspo #FashionTrends #OOTD #WardrobeEssentials
        - Food product → #FoodieLife #HealthyEating #MealPrep #CleanEating
        - Business tool → #Entrepreneur #SmallBusiness #Productivity #BusinessGrowth

        Generate ONLY the 4 community-focused hashtags for this specific product:"""

        ai_hashtags = self._generate_with_ollama(prompt, system_prompt)

        if ai_hashtags:
            # Extract just the hashtags (remove AI chatter)
            clean_hashtags = self._extract_hashtags_from_ai_response(ai_hashtags.strip())
            logger.info(f"🔍 Extracted clean hashtags: {clean_hashtags}")

            # Filter hashtags
            filtered_hashtags = self._filter_pinterest_content(clean_hashtags)

            # Remove any problematic hashtags
            problematic_hashtags = ['#GeneralCategory', '#GeneralHashtags', '#TechTrends', '#TechLife']
            for bad_hashtag in problematic_hashtags:
                filtered_hashtags = filtered_hashtags.replace(bad_hashtag, '')

            # Clean up extra spaces
            filtered_hashtags = ' '.join(filtered_hashtags.split())

            if filtered_hashtags:
                return filtered_hashtags

        return self._generate_hashtags(category, title)

    def _generate_hashtags(self, category: str, title: str) -> str:
        """Fallback hashtag generation if AI is unavailable"""
        base_tags = ["#PersonalDevelopment", "#SelfImprovement", "#Motivation", "#Success", "#Lifestyle"]

        category_tags = {
            'downloads': ["#DigitalProducts", "#OnlineLearning", "#Education"],
            'e-books': ["#Books", "#Reading", "#Knowledge", "#Learning"],
            'supplements': ["#Health", "#Wellness", "#Nutrition", "#Fitness"],
            'software': ["#Technology", "#Productivity", "#Tools", "#Software"],
            'health': ["#Health", "#Wellness", "#SelfCare", "#Healthy"]
        }

        # Add category-specific tags
        if category:
            cat_lower = category.lower()
            for key, tags in category_tags.items():
                if key in cat_lower:
                    base_tags.extend(tags[:3])  # Add up to 3 category tags
                    break

        return " ".join(base_tags[:10])  # Limit to 10 hashtags
    
    async def generate_complete_pinterest_content(self, product_id: int, title: str, affiliate_link: str, category: str = None) -> Dict:
        """Generate complete Pinterest content including Pexels video and product research"""

        # Clean title for Pinterest
        clean_title = self._clean_title_for_pinterest(title)

        # Research the product for better content generation
        logger.info(f"🔬 Researching product: {clean_title}")
        research_data = self._research_product(clean_title, affiliate_link)

        # Generate Pinterest form fields with research data
        pinterest_title = self.generate_pinterest_title(clean_title, category, research_data)
        pinterest_description = self.generate_pinterest_description_with_hashtags(clean_title, category, affiliate_link, research_data)

        # Use AI to generate video search query and select best video
        video_search_query = self.generate_ai_video_search_query(title, category, research_data, pinterest_title)
        pexels_video_data = await self.ai_select_best_video(video_search_query, title, pinterest_title, research_data)

        # Generate additional metadata
        marketing_angle = self._determine_marketing_angle(category, clean_title)
        search_terms = self._generate_search_terms(clean_title, category)
        topic_tags = self._generate_topic_tags(category)
        alt_text = f"{pinterest_title} - {pinterest_description[:100]}..."
        
        content = {
            # Pinterest Form Fields
            'pinterest_title': pinterest_title,
            'pinterest_description': pinterest_description,
            'affiliate_link': affiliate_link,
            'board_name': 'Affiliate Products',
            'tagged_topics': 'PLACEHOLDER_FOR_MANUAL_ENTRY',  # Requires special handling

            # Pexels Video Data
            'pexels_video_id': pexels_video_data.get('pexels_video_id') if pexels_video_data else None,
            'pexels_video_url': pexels_video_data.get('pexels_video_url') if pexels_video_data else None,
            'video_download_url': pexels_video_data.get('video_download_url') if pexels_video_data else None,
            'video_preview_image': pexels_video_data.get('video_preview_image') if pexels_video_data else None,
            'video_duration': pexels_video_data.get('video_duration') if pexels_video_data else None,
            'video_width': pexels_video_data.get('video_width') if pexels_video_data else None,
            'video_height': pexels_video_data.get('video_height') if pexels_video_data else None,
            'video_search_query': video_search_query,

            # Research Data
            'research_summary': json.dumps(research_data) if research_data else None,
            'research_quality': research_data.get('research_quality', 'none') if research_data else 'none',
            'researched_benefits': json.dumps(research_data.get('key_benefits', [])) if research_data else None,
            'researched_features': json.dumps(research_data.get('main_features', [])) if research_data else None,

            # Content Metadata
            'marketing_angle': marketing_angle,
            'search_terms': json.dumps(search_terms),
            'topic_tags': json.dumps(topic_tags),
            'alt_text': alt_text
        }
        
        return content
    
    def _clean_title_for_pinterest(self, title: str) -> str:
        """Clean product title for Pinterest compliance"""
        if not title:
            return "Amazing Product"

        # Remove commission/affiliate language first
        clean = title

        # Remove commission language patterns
        import re
        commission_patterns = [
            r'Earn \d+%.*?(?=\||\!|$)',  # "Earn 60% Commission Promoting..."
            r'\d+% commission.*?(?=\||\!|$)',  # "75% commission available"
            r'commission.*?(?=\||\!|$)',  # Any commission text
            r'affiliate.*?(?=\||\!|$)',  # Any affiliate text
            r'Promoting.*?(?=\||\!|$)',  # "Promoting a $37..."
            r'\$\d+.*?(?=\||\!|$)',  # Price mentions
            r'Easy affiliate sales.*?(?=\||\!|$)',  # "Easy affiliate sales!"
        ]

        for pattern in commission_patterns:
            clean = re.sub(pattern, '', clean, flags=re.IGNORECASE)

        # Remove category markers
        clean = clean.replace('| Downloads', '').replace('| E-books', '')
        clean = clean.replace('| Supplements - health', '').replace('| Deliverable', '')
        clean = clean.replace('| Software', '').replace('| In-person service', '')
        clean = clean.replace('| Audio book (download)', '').replace('| Book (printed)', '')

        # Remove quotes and excessive punctuation
        clean = clean.replace('"', '').replace("'", "")
        clean = clean.replace('–', '-').replace('—', '-')
        clean = clean.replace('**', '').replace('*', '')

        # Remove leading/trailing punctuation and whitespace
        clean = clean.strip(' |-!.')

        # Normalize whitespace
        clean = ' '.join(clean.split())

        # If title is too short after cleaning, create a better one
        if len(clean) < 10:
            return "Amazing Lifestyle Product"

        return clean.strip()
    
    def _determine_marketing_angle(self, category: str, title: str) -> str:
        """Determine marketing angle based on product"""
        if not category:
            return "transformation"
        
        category_lower = category.lower()
        if 'health' in category_lower or 'supplement' in category_lower:
            return "health_wellness"
        elif 'download' in category_lower or 'ebook' in category_lower:
            return "education_learning"
        elif 'software' in category_lower:
            return "productivity_tools"
        else:
            return "lifestyle_improvement"
    
    def _generate_search_terms(self, title: str, category: str) -> list:
        """Generate search terms for Pinterest SEO"""
        terms = []
        
        # Add words from title
        title_words = [word.lower() for word in title.split() if len(word) > 3]
        terms.extend(title_words[:5])
        
        # Add category-based terms
        if category:
            cat_lower = category.lower()
            if 'health' in cat_lower:
                terms.extend(['wellness', 'healthy living', 'self care'])
            elif 'download' in cat_lower:
                terms.extend(['digital products', 'online learning'])
            elif 'ebook' in cat_lower:
                terms.extend(['books', 'reading', 'knowledge'])
        
        # Add general terms
        terms.extend(['personal development', 'self improvement', 'motivation'])
        
        return list(set(terms))[:10]  # Remove duplicates, limit to 10
    
    def _generate_topic_tags(self, category: str) -> list:
        """Generate topic tags for Pinterest boards"""
        base_topics = ['personal development', 'self improvement', 'motivation']
        
        if category:
            cat_lower = category.lower()
            if 'health' in cat_lower:
                base_topics.extend(['health', 'wellness', 'fitness'])
            elif 'download' in cat_lower or 'ebook' in cat_lower:
                base_topics.extend(['education', 'learning', 'books'])
            elif 'software' in cat_lower:
                base_topics.extend(['productivity', 'technology', 'tools'])
        
        return base_topics[:5]

    def copy_products_from_source(self, limit: int = None):
        """Copy products from source database to Pinterest database"""
        logger.info("Copying products from source database...")

        # Read from source database
        source_conn = sqlite3.connect(self.source_db)
        source_cursor = source_conn.cursor()

        query = '''
            SELECT id, title, affiliate_link, scraped_at
            FROM products
            WHERE product_status = 'ACTIVE'
            AND affiliate_link IS NOT NULL
            AND title NOT LIKE 'Product %'
            AND title IS NOT NULL
            AND title != ''
        '''
        if limit:
            query += f" LIMIT {limit}"

        source_cursor.execute(query)
        source_products = source_cursor.fetchall()
        source_conn.close()

        if not source_products:
            logger.warning("No products found in source database")
            return 0

        # Insert into Pinterest database
        pinterest_conn = sqlite3.connect(self.pinterest_db)
        pinterest_cursor = pinterest_conn.cursor()

        copied_count = 0
        for source_id, title, affiliate_link, scraped_at in source_products:
            try:
                # Check if already exists
                pinterest_cursor.execute(
                    'SELECT id FROM products WHERE source_product_id = ?',
                    (source_id,)
                )
                if pinterest_cursor.fetchone():
                    continue  # Skip if already exists

                # Extract category from title
                category = None
                if '|' in title:
                    category = title.split('|')[-1].strip()

                # Insert new product
                pinterest_cursor.execute('''
                    INSERT INTO products (
                        source_product_id, title, affiliate_link, category, scraped_at
                    ) VALUES (?, ?, ?, ?, ?)
                ''', (source_id, title, affiliate_link, category, scraped_at))

                copied_count += 1

            except Exception as e:
                logger.error(f"Error copying product {title}: {e}")
                continue

        pinterest_conn.commit()
        pinterest_conn.close()

        logger.info(f"Copied {copied_count} products to Pinterest database")
        return copied_count

    async def process_products_for_pinterest(self, batch_size: int = 10):
        """Process products and generate Pinterest content with Pexels videos"""
        logger.info("Processing products for Pinterest content generation...")

        conn = sqlite3.connect(self.pinterest_db)
        cursor = conn.cursor()

        # Get unprocessed products
        cursor.execute('''
            SELECT id, title, affiliate_link, category
            FROM products
            WHERE processed_for_pinterest = FALSE
            LIMIT ?
        ''', (batch_size,))

        products = cursor.fetchall()

        if not products:
            logger.info("No unprocessed products found")
            return 0

        processed_count = 0
        for product_id, title, affiliate_link, category in products:
            try:
                logger.info(f"Processing: {title[:50]}...")

                # Generate complete Pinterest content with Pexels video
                content = await self.generate_complete_pinterest_content(
                    product_id, title, affiliate_link, category
                )

                # Store Pinterest content with research data
                cursor.execute('''
                    INSERT INTO pinterest_content (
                        product_id, pinterest_title, pinterest_description, affiliate_link,
                        board_name, tagged_topics, pexels_video_id, pexels_video_url,
                        video_download_url, video_preview_image, video_duration,
                        video_width, video_height, video_search_query,
                        research_summary, research_quality, researched_benefits, researched_features,
                        marketing_angle, search_terms, topic_tags, alt_text
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    product_id,
                    content['pinterest_title'],
                    content['pinterest_description'],
                    content['affiliate_link'],
                    content['board_name'],
                    content['tagged_topics'],
                    content['pexels_video_id'],
                    content['pexels_video_url'],
                    content['video_download_url'],
                    content['video_preview_image'],
                    content['video_duration'],
                    content['video_width'],
                    content['video_height'],
                    content['video_search_query'],
                    content['research_summary'],
                    content['research_quality'],
                    content['researched_benefits'],
                    content['researched_features'],
                    content['marketing_angle'],
                    content['search_terms'],
                    content['topic_tags'],
                    content['alt_text']
                ))

                # Mark product as processed
                cursor.execute('''
                    UPDATE products
                    SET processed_for_pinterest = TRUE, content_generated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (product_id,))

                processed_count += 1
                logger.info(f"✅ Generated content with video: {content['video_download_url'] is not None}")

            except Exception as e:
                logger.error(f"Error processing product {title}: {e}")
                continue

        conn.commit()
        conn.close()

        logger.info(f"Generated Pinterest content for {processed_count} products")
        return processed_count

    def get_database_stats(self):
        """Get statistics about both databases"""
        # Source database stats
        source_conn = sqlite3.connect(self.source_db)
        source_cursor = source_conn.cursor()
        source_cursor.execute('SELECT COUNT(*) FROM products WHERE product_status = "ACTIVE"')
        source_active = source_cursor.fetchone()[0]
        source_conn.close()

        # Pinterest database stats
        pinterest_conn = sqlite3.connect(self.pinterest_db)
        pinterest_cursor = pinterest_conn.cursor()

        pinterest_cursor.execute('SELECT COUNT(*) FROM products')
        pinterest_products = pinterest_cursor.fetchone()[0]

        pinterest_cursor.execute('SELECT COUNT(*) FROM products WHERE processed_for_pinterest = TRUE')
        processed_products = pinterest_cursor.fetchone()[0]

        pinterest_cursor.execute('SELECT COUNT(*) FROM pinterest_content')
        content_generated = pinterest_cursor.fetchone()[0]

        pinterest_cursor.execute('SELECT COUNT(*) FROM pinterest_content WHERE video_download_url IS NOT NULL')
        content_with_videos = pinterest_cursor.fetchone()[0]

        pinterest_conn.close()

        return {
            'source_active_products': source_active,
            'pinterest_products': pinterest_products,
            'processed_products': processed_products,
            'content_generated': content_generated,
            'content_with_videos': content_with_videos
        }

async def main():
    """Main function to process products and generate Pinterest content"""
    generator = EnhancedPinterestGenerator()

    # Show initial stats
    stats = generator.get_database_stats()
    logger.info("📊 DATABASE STATS:")
    logger.info(f"   Source DB active products: {stats['source_active_products']}")
    logger.info(f"   Pinterest DB products: {stats['pinterest_products']}")
    logger.info(f"   Processed products: {stats['processed_products']}")
    logger.info(f"   Content generated: {stats['content_generated']}")
    logger.info(f"   Content with videos: {stats['content_with_videos']}")

    # Copy products from source (limit to 20 for demo)
    copied = generator.copy_products_from_source(limit=20)

    # Generate Pinterest content with videos for unprocessed products
    processed = await generator.process_products_for_pinterest(batch_size=10)

    # Show final stats
    final_stats = generator.get_database_stats()
    logger.info("📊 FINAL STATS:")
    logger.info(f"   Pinterest DB products: {final_stats['pinterest_products']}")
    logger.info(f"   Processed products: {final_stats['processed_products']}")
    logger.info(f"   Content generated: {final_stats['content_generated']}")
    logger.info(f"   Content with videos: {final_stats['content_with_videos']}")

if __name__ == "__main__":
    asyncio.run(main())
