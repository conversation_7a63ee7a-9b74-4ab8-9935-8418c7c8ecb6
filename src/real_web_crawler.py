#!/usr/bin/env python3
"""
Real Web Crawler for Product Research
Uses actual web search and comprehensive site crawling
"""

import json
import sys
import requests
import time
import re
from urllib.parse import urljoin, urlparse, quote_plus
from typing import Dict, List, Optional
from loguru import logger

class RealWebCrawler:
    """
    Real web crawler that uses actual web search and crawls entire websites
    """
    
    def __init__(self):
        self.max_pages_per_site = 8
        self.request_delay = 0.8
        self.timeout = 15
        
    def research_product(self, product_name: str, affiliate_link: str = None) -> Dict:
        """
        Research a product by following the affiliate link to the actual product site
        ONLY uses real affiliate URLs - no generic web search fallbacks
        """
        try:
            logger.info(f"🔍 Researching product: {product_name}")

            # ONLY use the affiliate link - no fallbacks to generic searches
            if not affiliate_link or not self._is_valid_url(affiliate_link):
                logger.error("❌ No valid affiliate link provided - cannot research product without real URL")
                return self._create_no_research_result(product_name, "No affiliate link provided")

            logger.info(f"🔗 Following affiliate link: {affiliate_link}")

            # Follow the affiliate link to get the actual product site
            actual_product_url = self._follow_affiliate_link(affiliate_link)

            if not actual_product_url:
                logger.error("❌ Could not follow affiliate link to product site")
                return self._create_no_research_result(product_name, "Affiliate link failed")

            search_results = [{
                'title': f'{product_name} - Official Product Page',
                'url': actual_product_url,
                'snippet': f'Official product information for {product_name}'
            }]

            # Step 2: Crawl the actual product website
            all_content = []
            all_benefits = []
            all_features = []
            crawled_sites = 0

            for result in search_results:
                try:
                    logger.info(f"🕷️ Crawling actual product site: {result['url']}")

                    site_data = self._crawl_entire_site(result['url'])

                    if site_data:
                        crawled_sites += 1

                        # Extract content from all pages
                        for page in site_data:
                            if page.get('content'):
                                all_content.append(page['content'])

                                # Extract benefits and features
                                benefits = self._extract_benefits(page['content'])
                                features = self._extract_features(page['content'])

                                all_benefits.extend(benefits)
                                all_features.extend(features)

                        logger.info(f"✅ Crawled {len(site_data)} pages from actual product site")

                        # Respectful delay between sites
                        time.sleep(self.request_delay * 2)

                except Exception as e:
                    logger.warning(f"Failed to crawl product site {result.get('url', '')}: {e}")
                    continue

            # Step 3: Compile comprehensive research from actual product site
            unique_benefits = list(dict.fromkeys(all_benefits))[:12]  # Top 12 unique benefits
            unique_features = list(dict.fromkeys(all_features))[:10]   # Top 10 unique features

            # Extract actual product name from content
            actual_product_name = product_name  # Default fallback
            if all_content:
                combined_content = ' '.join(all_content)
                extracted_name = self._extract_product_name(combined_content)
                if extracted_name:
                    actual_product_name = extracted_name
                    logger.info(f"✅ Extracted actual product name: {actual_product_name}")

            research_data = {
                'product_name': product_name,
                'actual_product_name': actual_product_name,  # Add extracted product name
                'affiliate_link': affiliate_link,
                'actual_product_url': search_results[0]['url'] if search_results else None,
                'search_results': search_results,
                'crawled_sites': crawled_sites,
                'total_pages_crawled': len(all_content),
                'key_benefits': unique_benefits,
                'main_features': unique_features,
                'research_quality': 'comprehensive' if crawled_sites > 0 else 'basic',
                'content_summary': ' '.join(all_content)[:3000] if all_content else ''
            }

            logger.info(f"✅ Research complete from actual product site: {crawled_sites} sites, {len(all_content)} pages, {len(unique_benefits)} benefits, {len(unique_features)} features")

            return research_data

        except Exception as e:
            logger.error(f"Product research failed: {e}")
            return self._create_no_research_result(product_name, f"Research failed: {e}")

    def _follow_affiliate_link(self, affiliate_link: str) -> Optional[str]:
        """
        Follow the affiliate link to find the actual product landing page
        """
        try:
            logger.info(f"🔗 Following affiliate link to actual product site")

            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Connection': 'keep-alive',
            }

            # Follow the affiliate link with redirects
            response = requests.get(affiliate_link, headers=headers, timeout=15, allow_redirects=True)

            if response.status_code == 200:
                # Get the final URL after all redirects
                final_url = response.url

                logger.info(f"✅ Affiliate link redirected to: {final_url}")

                # Validate that we got to a real product page
                if self._is_product_page(response.text, final_url):
                    return final_url
                else:
                    logger.warning(f"Final URL doesn't appear to be a product page: {final_url}")
                    return None
            else:
                logger.warning(f"Failed to follow affiliate link: HTTP {response.status_code}")
                return None

        except Exception as e:
            logger.error(f"Failed to follow affiliate link: {e}")
            return None

    def _is_product_page(self, content: str, url: str) -> bool:
        """
        Check if the page appears to be a product page
        """
        try:
            content_lower = content.lower()

            # Look for product page indicators
            product_indicators = [
                'buy now', 'add to cart', 'purchase', 'order now', 'get now',
                'product', 'benefits', 'features', 'ingredients', 'testimonials',
                'reviews', 'guarantee', 'money back', 'price', 'discount',
                'supplement', 'formula', 'system', 'program', 'guide', 'course'
            ]

            # Count how many indicators we find
            indicator_count = sum(1 for indicator in product_indicators if indicator in content_lower)

            # Also check URL for product-like patterns
            url_lower = url.lower()
            url_indicators = [
                'product', 'buy', 'order', 'shop', 'store', 'checkout',
                'supplement', 'health', 'wellness', 'fitness'
            ]

            url_indicator_count = sum(1 for indicator in url_indicators if indicator in url_lower)

            # Consider it a product page if we have enough indicators
            total_score = indicator_count + (url_indicator_count * 2)  # URL indicators worth more

            logger.debug(f"Product page score: {total_score} (content: {indicator_count}, url: {url_indicator_count})")

            return total_score >= 3  # Threshold for considering it a product page

        except Exception as e:
            logger.debug(f"Error checking if product page: {e}")
            return True  # Default to true if we can't check

    # REMOVED: No generic web search - only use affiliate URLs
    
    def _crawl_entire_site(self, base_url: str) -> List[Dict]:
        """
        Crawl an entire website to find all product-related information
        """
        try:
            if not self._is_valid_url(base_url):
                return []
            
            logger.debug(f"🌐 Starting site crawl: {base_url}")
            
            # Parse base URL
            parsed_base = urlparse(base_url)
            base_domain = f"{parsed_base.scheme}://{parsed_base.netloc}"
            
            # Get main page content
            main_content = self._fetch_clean_content(base_url)
            if not main_content:
                return []
            
            crawled_pages = [{
                'url': base_url,
                'content': main_content,
                'page_type': 'main'
            }]
            
            # Find internal links
            internal_links = self._extract_internal_links(base_url, main_content)
            
            # Add common product pages
            common_paths = [
                '/about', '/benefits', '/features', '/how-it-works', '/product',
                '/info', '/details', '/overview', '/ingredients', '/testimonials',
                '/reviews', '/faq', '/science', '/research', '/order', '/buy'
            ]
            
            for path in common_paths:
                potential_url = base_domain + path
                if potential_url not in [link['url'] for link in crawled_pages]:
                    internal_links.append(potential_url)
            
            # Crawl additional pages
            for link in internal_links[:self.max_pages_per_site]:
                try:
                    content = self._fetch_clean_content(link)
                    if content and len(content) > 200:  # Meaningful content only
                        crawled_pages.append({
                            'url': link,
                            'content': content,
                            'page_type': self._determine_page_type(link)
                        })
                    
                    time.sleep(self.request_delay)
                    
                except Exception as e:
                    logger.debug(f"Failed to crawl {link}: {e}")
                    continue
            
            return crawled_pages
            
        except Exception as e:
            logger.error(f"Site crawling failed: {e}")
            return []
    
    def _fetch_clean_content(self, url: str) -> Optional[str]:
        """
        Fetch and clean content from a web page
        """
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Connection': 'keep-alive',
            }
            
            response = requests.get(url, headers=headers, timeout=self.timeout, allow_redirects=True)
            
            if response.status_code == 200:
                content = response.text
                
                # Advanced content cleaning
                # Remove scripts, styles, and other non-content
                content = re.sub(r'<script[^>]*>.*?</script>', '', content, flags=re.DOTALL | re.IGNORECASE)
                content = re.sub(r'<style[^>]*>.*?</style>', '', content, flags=re.DOTALL | re.IGNORECASE)
                content = re.sub(r'<noscript[^>]*>.*?</noscript>', '', content, flags=re.DOTALL | re.IGNORECASE)
                content = re.sub(r'<head[^>]*>.*?</head>', '', content, flags=re.DOTALL | re.IGNORECASE)
                
                # Remove navigation and footer content
                content = re.sub(r'<nav[^>]*>.*?</nav>', '', content, flags=re.DOTALL | re.IGNORECASE)
                content = re.sub(r'<footer[^>]*>.*?</footer>', '', content, flags=re.DOTALL | re.IGNORECASE)
                content = re.sub(r'<header[^>]*>.*?</header>', '', content, flags=re.DOTALL | re.IGNORECASE)

                # Remove cookie notices and privacy content
                content = re.sub(r'cookie.*?consent.*?accept', '', content, flags=re.IGNORECASE | re.DOTALL)
                content = re.sub(r'privacy.*?policy', '', content, flags=re.IGNORECASE)

                # Remove HTML tags but preserve line breaks
                content = re.sub(r'<br[^>]*>', '\n', content, flags=re.IGNORECASE)
                content = re.sub(r'<[^>]+>', ' ', content)

                # Clean up text but preserve important punctuation
                content = re.sub(r'\s+', ' ', content)
                content = re.sub(r'[^\w\s.,!?;:()\-$%&]', ' ', content)

                # Remove JavaScript and CSS remnants
                content = re.sub(r'function\s*\([^)]*\)\s*{[^}]*}', '', content, flags=re.DOTALL)
                content = re.sub(r'var\s+\w+\s*=.*?;', '', content)
                content = re.sub(r'window\.\w+.*?;', '', content)
                content = re.sub(r'@media[^{]*{[^}]*}', '', content, flags=re.DOTALL)
                content = re.sub(r'[.#]\w+\s*{[^}]*}', '', content, flags=re.DOTALL)

                # Extract product name/title from page content
                product_title = ""
                title_patterns = [
                    # Look for specific product names in common affiliate page structures
                    r'(?:introducing|meet|discover)\s+([A-Z][a-zA-Z\s]+?)(?:\s*[,.]|\s*is|\s*-)',
                    r'([A-Z][a-zA-Z]+(?:\s+[A-Z][a-zA-Z]+)*)\s+is\s+(?:unlike|the\s+only|a\s+unique)',
                    r'That\'s why we created\s+([A-Z][a-zA-Z\s]+?)(?:\s*[,.]|\s*$)',
                    r'([A-Z][a-zA-Z]+(?:\s+[A-Z][a-zA-Z]+)*)\s+contains?\s+(?:nine|natural|powerful)',
                    # Look for product names in titles and headers
                    r'<title[^>]*>([^<|]+?)(?:\s*\||\s*-|\s*–|$)',
                    r'<h1[^>]*>([^<]+)</h1>',
                    r'<h2[^>]*>([^<]+)</h2>',
                ]

                for pattern in title_patterns:
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    if matches:
                        candidate = matches[0].strip()
                        # Filter out generic terms and keep actual product names
                        if len(candidate) > 3 and len(candidate) < 50:
                            # Avoid generic terms but allow short branded names
                            generic_terms = ['supplement', 'formula', 'product', 'solution', 'system', 'method', 'program']
                            if not any(term in candidate.lower() for term in generic_terms) or len(candidate) < 20:
                                product_title = candidate
                                break

                # Extract meaningful sentences and phrases
                sentences = [s.strip() for s in content.split('.') if len(s.strip()) > 15]

                # Filter for product-related content with comprehensive keywords for affiliate products
                product_keywords = [
                    # General product terms
                    'benefit', 'feature', 'help', 'improve', 'enhance', 'support', 'provide',
                    'deliver', 'offer', 'include', 'contain', 'design', 'create', 'develop',
                    'natural', 'effective', 'proven', 'research', 'study', 'result', 'user',
                    'ingredient', 'formula', 'supplement', 'program', 'system', 'method',
                    'product', 'solution', 'treatment', 'therapy', 'protocol', 'technique',

                    # Health & wellness terms
                    'health', 'wellness', 'fitness', 'nutrition', 'vitamin', 'mineral',
                    'amino', 'acid', 'protein', 'muscle', 'strength', 'energy', 'endurance',
                    'recovery', 'performance', 'metabolism', 'immune', 'antioxidant',

                    # Brain & cognitive terms
                    'brain', 'cognitive', 'memory', 'focus', 'learning', 'intelligence',
                    'neuroscience', 'scientist', 'discovery', 'breakthrough', 'technology',
                    'genius', 'wave', 'trick', 'activate', 'superbrain', 'theta',
                    'creativity', 'mental', 'clarity', 'concentration', 'alertness',

                    # Manifestation & spiritual terms
                    'manifestation', 'abundance', 'wealth', 'prosperity', 'success',
                    'pineal', 'gland', 'chakra', 'meditation', 'spiritual', 'consciousness',

                    # Anti-aging & longevity terms
                    'aging', 'longevity', 'youthful', 'rejuvenation', 'cellular', 'repair',
                    'regeneration', 'vitality', 'lifespan', 'healthspan'
                ]

                relevant_sentences = []
                for sentence in sentences[:200]:  # First 200 sentences
                    sentence_lower = sentence.lower()
                    if any(keyword in sentence_lower for keyword in product_keywords):
                        if 15 <= len(sentence) <= 500:  # More permissive length
                            relevant_sentences.append(sentence)

                if relevant_sentences:
                    clean_content = '. '.join(relevant_sentences[:20])  # Top 20 relevant sentences
                else:
                    # More permissive fallback - take more content
                    clean_content = ' '.join(content.split()[:1200])  # First 1200 words
                
                return clean_content.strip() if len(clean_content.strip()) > 50 else None
            
            return None
            
        except Exception as e:
            logger.debug(f"Failed to fetch {url}: {e}")
            return None
    
    def _extract_internal_links(self, base_url: str, content: str) -> List[str]:
        """
        Extract internal links from page content
        """
        try:
            parsed_base = urlparse(base_url)
            base_domain = f"{parsed_base.scheme}://{parsed_base.netloc}"
            
            # Find href links
            link_pattern = r'href=["\']([^"\']+)["\']'
            raw_links = re.findall(link_pattern, content, re.IGNORECASE)
            
            internal_links = []
            for link in raw_links:
                # Convert to absolute URL
                if link.startswith('/'):
                    full_link = base_domain + link
                elif link.startswith('http'):
                    if urlparse(link).netloc == parsed_base.netloc:
                        full_link = link
                    else:
                        continue  # Skip external links
                else:
                    full_link = urljoin(base_url, link)
                
                # Filter out unwanted links
                if any(skip in full_link.lower() for skip in ['#', 'javascript:', 'mailto:', 'tel:', '.pdf', '.jpg', '.png', '.gif', '.css', '.js']):
                    continue
                
                if full_link not in internal_links:
                    internal_links.append(full_link)
            
            return internal_links[:20]  # Limit to 20 links
            
        except Exception as e:
            logger.debug(f"Link extraction failed: {e}")
            return []
    
    def _extract_benefits(self, content: str) -> List[str]:
        """Extract benefits from content"""
        if not content:
            return []

        benefits = []

        # Enhanced benefit patterns for sales pages
        benefit_patterns = [
            r'activates?\s+([^.!?]{10,100})',
            r'restores?\s+([^.!?]{10,100})',
            r'unlocks?\s+([^.!?]{10,100})',
            r'boosts?\s+([^.!?]{10,100})',
            r'enhances?\s+([^.!?]{10,100})',
            r'improves?\s+([^.!?]{10,100})',
            r'increases?\s+([^.!?]{10,100})',
            r'gives?\s+you\s+([^.!?]{10,100})',
            r'helps?\s+you\s+([^.!?]{10,100})',
            r'provides?\s+([^.!?]{10,100})',
            r'delivers?\s+([^.!?]{10,100})',
            r'brain\s+([^.!?]{10,100})',
            r'genius\s+([^.!?]{10,100})',
            r'creativity\s+([^.!?]{10,100})',
            r'theta\s+([^.!?]{10,100})',
            r'[•·▪▫◦‣⁃]\s*([^.!?\n]{10,100})',
            r'✓\s*([^.!?\n]{10,100})',
        ]

        # Look for specific product claims from actual content
        content_lower = content.lower()

        # Extract specific claims based on actual content patterns (not hardcoded)
        # Look for common affiliate product claim patterns
        claim_patterns = [
            r'(?:this|it|formula|system|program|method)\s+(?:will|can|helps?|allows?)\s+([^.!?]{15,120})',
            r'(?:you\s+(?:will|can|get|receive|experience))\s+([^.!?]{15,120})',
            r'(?:proven\s+to|shown\s+to|designed\s+to)\s+([^.!?]{15,120})',
            r'(?:clinically|scientifically)\s+(?:proven|tested|validated)\s+([^.!?]{15,120})',
            r'(?:results\s+include|benefits\s+include)\s*:?\s*([^.!?]{15,120})',
        ]

        for pattern in claim_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            for match in matches[:3]:  # Top 3 per pattern
                clean_claim = ' '.join(match.strip().split())
                if len(clean_claim) > 10 and len(clean_claim) < 150:
                    if clean_claim not in benefits:
                        benefits.append(clean_claim)

        # General pattern matching
        for pattern in benefit_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            for match in matches[:2]:  # Top 2 per pattern
                clean_benefit = ' '.join(match.strip().split())
                if len(clean_benefit) > 8 and len(clean_benefit) < 120:
                    if clean_benefit not in benefits:
                        benefits.append(clean_benefit)

        return benefits[:12]  # Top 12 benefits
    
    def _extract_features(self, content: str) -> List[str]:
        """Extract features from content"""
        if not content:
            return []
        
        features = []
        feature_patterns = [
            r'features?[:\s]+([^.!?]{15,150})',
            r'includes?\s+([^.!?]{15,150})',
            r'contains?\s+([^.!?]{15,150})',
            r'made\s+with\s+([^.!?]{15,150})',
            r'formulated\s+with\s+([^.!?]{15,150})',
        ]
        
        for pattern in feature_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            for match in matches[:2]:  # Top 2 per pattern
                clean_feature = ' '.join(match.strip().split())
                if clean_feature and clean_feature not in features:
                    features.append(clean_feature)
        
        return features[:8]  # Top 8 features
    
    def _is_valid_url(self, url: str) -> bool:
        """Check if URL is valid and accessible"""
        try:
            parsed = urlparse(url)
            return bool(parsed.netloc) and parsed.scheme in ['http', 'https']
        except:
            return False

    def _extract_product_name(self, content: str) -> Optional[str]:
        """Extract actual product name from content"""
        if not content:
            return None

        # Patterns to find actual product names in affiliate content
        name_patterns = [
            # Look for "That's why we created [ProductName]"
            r'That\'s why we created\s+([A-Z][a-zA-Z\s]+?)(?:\s*[,.]|\s*$)',
            # Look for "[ProductName] is unlike anything"
            r'([A-Z][a-zA-Z]+(?:\s+[A-Z][a-zA-Z]+)*)\s+is\s+(?:unlike|the\s+only|a\s+unique)',
            # Look for "Introducing [ProductName]"
            r'(?:introducing|meet|discover)\s+([A-Z][a-zA-Z\s]+?)(?:\s*[,.]|\s*is|\s*-)',
            # Look for "[ProductName] contains"
            r'([A-Z][a-zA-Z]+(?:\s+[A-Z][a-zA-Z]+)*)\s+contains?\s+(?:nine|natural|powerful)',
            # Look for product names in titles
            r'<title[^>]*>([^<|]+?)(?:\s*\||\s*-|\s*–|$)',
            # Look for branded names that appear multiple times
            r'\b([A-Z][a-zA-Z]{3,15}(?:\s+[A-Z][a-zA-Z]{3,15})?)\b',
        ]

        for pattern in name_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                for candidate in matches:
                    candidate = candidate.strip()
                    # Filter criteria for valid product names
                    if (len(candidate) >= 4 and len(candidate) <= 30 and
                        not any(generic in candidate.lower() for generic in
                               ['supplement', 'formula', 'product', 'solution', 'system', 'website', 'page', 'site']) and
                        candidate[0].isupper()):
                        return candidate

        return None

    def _determine_page_type(self, url: str) -> str:
        """Determine page type from URL"""
        url_lower = url.lower()
        if any(word in url_lower for word in ['about', 'info']):
            return 'about'
        elif any(word in url_lower for word in ['benefit', 'feature']):
            return 'benefits'
        elif any(word in url_lower for word in ['how', 'work', 'science']):
            return 'how_it_works'
        elif any(word in url_lower for word in ['testimonial', 'review']):
            return 'testimonials'
        elif any(word in url_lower for word in ['ingredient', 'formula']):
            return 'ingredients'
        else:
            return 'general'
    
    def _create_no_research_result(self, product_name: str, reason: str) -> Dict:
        """Create empty research result when affiliate URL fails - no fallback searches"""
        logger.warning(f"⚠️ No research data available for {product_name}: {reason}")
        return {
            'product_name': product_name,
            'affiliate_link': None,
            'actual_product_url': None,
            'search_results': [],
            'crawled_sites': 0,
            'total_pages_crawled': 0,
            'key_benefits': [],
            'main_features': [],
            'research_quality': 'none',
            'content_summary': f'No research available for {product_name} - {reason}',
            'error': reason
        }

def main():
    """Command line interface"""
    if len(sys.argv) < 2:
        print("Usage: python3 real_web_crawler.py <product_name> [affiliate_link]")
        sys.exit(1)

    product_name = sys.argv[1]
    affiliate_link = sys.argv[2] if len(sys.argv) > 2 else None

    crawler = RealWebCrawler()
    research_data = crawler.research_product(product_name, affiliate_link)

    print(json.dumps(research_data, indent=2))

if __name__ == "__main__":
    main()
