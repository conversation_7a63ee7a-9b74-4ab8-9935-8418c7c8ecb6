#!/usr/bin/env python3
"""
Pinterest Database Viewer - View scraped products and generated Pinterest content
"""

import sqlite3
import sys
import json

def view_database(db_path="pinterest_content_enhanced.db"):
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Get database stats
        cursor.execute('SELECT COUNT(*) FROM products')
        total_products = cursor.fetchone()[0]

        cursor.execute('SELECT COUNT(*) FROM pinterest_content')
        total_content = cursor.fetchone()[0]

        cursor.execute('SELECT COUNT(*) FROM products WHERE processed_for_pinterest = TRUE')
        processed_products = cursor.fetchone()[0]

        cursor.execute('SELECT COUNT(*) FROM pinterest_content WHERE pexels_video_url IS NOT NULL')
        content_with_videos = cursor.fetchone()[0]

        print("🎯 PINTEREST AFFILIATE DATABASE STATS")
        print("=" * 50)
        print(f"📊 Total products: {total_products}")
        print(f"📝 Pinterest content generated: {total_content}")
        print(f"✅ Processed products: {processed_products}")
        print(f"🎬 Content with videos: {content_with_videos}")
        print()

        if total_products > 0:
            print("📦 RECENT PRODUCTS:")
            print("-" * 30)
            cursor.execute('''
                SELECT id, title, category, affiliate_link, processed_for_pinterest
                FROM products
                ORDER BY id DESC
                LIMIT 10
            ''')

            for row in cursor.fetchall():
                product_id, title, category, affiliate_link, processed = row
                status = "✅ Processed" if processed else "⏳ Pending"
                print(f"Product {product_id}: {title[:50]}...")
                print(f"  📂 Category: {category}")
                print(f"  🔗 Link: {affiliate_link[:60]}...")
                print(f"  📊 Status: {status}")
                print()

        if total_content > 0:
            print("🎨 PINTEREST CONTENT READY FOR POSTING:")
            print("-" * 45)
            cursor.execute('''
                SELECT pc.product_id, p.title, pc.pinterest_title, pc.pinterest_description,
                       pc.affiliate_link, pc.pexels_video_url, pc.video_download_url, pc.generated_at
                FROM pinterest_content pc
                JOIN products p ON pc.product_id = p.id
                ORDER BY pc.product_id
                LIMIT 10
            ''')

            for row in cursor.fetchall():
                product_id, product_title, pinterest_title, description, affiliate_link, video_url, download_url, generated_at = row
                video_status = "🎬 Ready" if video_url else "❌ Missing"

                print(f"📌 PRODUCT {product_id}: {pinterest_title}")
                print(f"   📦 Original: {product_title[:50]}...")
                print(f"   📝 Description: {description[:80]}...")
                print(f"   🔗 Affiliate: {affiliate_link[:50]}...")
                print(f"   🎬 Video: {video_status}")
                if download_url:
                    print(f"   📥 Download: {download_url[:50]}...")
                print(f"   📅 Generated: {generated_at}")
                print()

            print("💡 Use: python3 src/view_database.py pinterest_content_enhanced.db [product_id] for details")

        conn.close()

    except Exception as e:
        print(f"❌ Error: {e}")
        print(f"💡 Make sure the database file exists: {db_path}")

def view_specific_product(db_path, product_id):
    """View detailed information for a specific product"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Get product info
        cursor.execute('SELECT * FROM products WHERE id = ?', (product_id,))
        product = cursor.fetchone()

        if not product:
            print(f"❌ Product {product_id} not found")
            return

        print(f"🎯 PRODUCT {product_id} DETAILS:")
        print("=" * 40)
        print(f"📦 Title: {product[2]}")  # title
        print(f"📂 Category: {product[4]}")  # category
        print(f"🔗 Affiliate Link: {product[3]}")  # affiliate_link
        print(f"📅 Scraped: {product[5]}")  # scraped_at
        print(f"✅ Processed: {product[6]}")  # processed_for_pinterest
        print()

        # Get Pinterest content with all posting fields
        cursor.execute('SELECT * FROM pinterest_content WHERE product_id = ?', (product_id,))
        content = cursor.fetchone()

        if content:
            print("🎨 PINTEREST POSTING CONTENT:")
            print("-" * 35)
            print(f"📝 Pinterest Title: {content[2]}")  # pinterest_title
            print()
            print(f"📄 Pinterest Description:")
            print(f"{content[3]}")  # pinterest_description (full description with hashtags)
            print()
            print(f"🔗 Affiliate Link: {content[4]}")  # affiliate_link
            print()
            print("🎬 VIDEO INFORMATION:")
            print(f"   📺 Pexels Video URL: {content[7] if content[7] else 'None'}")  # pexels_video_url
            print(f"   📥 Download URL: {content[8] if content[8] else 'None'}")  # video_download_url
            print(f"   🖼️ Preview Image: {content[9] if content[9] else 'None'}")  # video_preview_image
            if content[10]:  # video_duration
                print(f"   ⏱️ Duration: {content[10]} seconds")
            if content[11] and content[12]:  # video_width, video_height
                print(f"   📐 Dimensions: {content[11]}x{content[12]}")
            print(f"   🔍 Search Query: {content[13] if content[13] else 'None'}")  # video_search_query
            print()
            print("📊 METADATA:")
            print(f"   📅 Generated: {content[21]}")  # generated_at
            print(f"   ✅ Used: {'Yes' if content[22] else 'No'}")  # is_used
            if content[23]:  # posted_at
                print(f"   📌 Posted: {content[23]}")
        else:
            print("⏳ No Pinterest content generated yet")

        conn.close()

    except Exception as e:
        print(f"❌ Error: {e}")

def interactive_menu(db_path):
    """Interactive menu to choose products"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        while True:
            print("\n🎯 PINTEREST DATABASE VIEWER")
            print("=" * 35)
            print("1. 📊 View database overview")
            print("2. 📌 View specific product content")
            print("3. 📋 List all products")
            print("4. 🚪 Exit")

            choice = input("\nChoose option (1-4): ").strip()

            if choice == "1":
                print("\n")
                view_database(db_path)
            elif choice == "2":
                cursor.execute('SELECT id, title FROM products ORDER BY id')
                products = cursor.fetchall()

                print("\n📋 AVAILABLE PRODUCTS:")
                print("-" * 25)
                for product_id, title in products:
                    print(f"{product_id:2d}. {title[:60]}...")

                try:
                    product_id = int(input("\nEnter product ID: ").strip())
                    print("\n")
                    view_specific_product(db_path, product_id)
                except ValueError:
                    print("❌ Invalid product ID")
            elif choice == "3":
                cursor.execute('SELECT id, title, category FROM products ORDER BY id')
                products = cursor.fetchall()

                print("\n📋 ALL PRODUCTS:")
                print("-" * 20)
                for product_id, title, category in products:
                    print(f"{product_id:2d}. {title[:50]}... [{category}]")
            elif choice == "4":
                print("👋 Goodbye!")
                break
            else:
                print("❌ Invalid choice")

        conn.close()

    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    if len(sys.argv) > 2 and sys.argv[2].isdigit():
        # View specific product: python3 view_database.py db_path product_id
        db_path = sys.argv[1]
        product_id = int(sys.argv[2])
        view_specific_product(db_path, product_id)
    elif len(sys.argv) > 1 and sys.argv[1] == "menu":
        # Interactive menu: python3 view_database.py menu
        interactive_menu("pinterest_content_enhanced.db")
    elif len(sys.argv) > 2 and sys.argv[1] == "menu":
        # Interactive menu with custom db: python3 view_database.py menu db_path
        interactive_menu(sys.argv[2])
    else:
        # View database overview
        db_path = sys.argv[1] if len(sys.argv) > 1 else "pinterest_content_enhanced.db"
        view_database(db_path)
