#!/usr/bin/env python3
"""
Test Multiple Videos Feature
Generate Pinterest content for Product 1 with multiple video options
"""

import asyncio
import os
import sys
import sqlite3
import logging

# Add src directory to path
sys.path.append('src')

from enhanced_pinterest_generator import EnhancedPinterestGenerator

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def view_multiple_videos(product_id: int = 1):
    """View multiple video options for a product"""
    try:
        conn = sqlite3.connect('pinterest_content_enhanced.db')
        cursor = conn.cursor()
        
        # Get product info
        cursor.execute('SELECT title FROM products WHERE id = ?', (product_id,))
        product = cursor.fetchone()
        
        if not product:
            logger.error(f"Product {product_id} not found")
            return
            
        title = product[0]
        logger.info(f"📌 Product: {title[:60]}...")
        
        # Get video options
        cursor.execute('''
            SELECT rank, pexels_video_url, video_download_url, video_tags, video_duration
            FROM video_options 
            WHERE product_id = ? 
            ORDER BY rank
        ''', (product_id,))
        
        videos = cursor.fetchall()
        
        if videos:
            logger.info(f"🎬 Found {len(videos)} video options:")
            for rank, video_url, download_url, tags, duration in videos:
                logger.info(f"   #{rank}: {video_url}")
                logger.info(f"        Tags: {tags}")
                logger.info(f"        Duration: {duration}s")
                logger.info(f"        Download: {download_url}")
                logger.info("")
        else:
            logger.info("❌ No video options found")
            
        conn.close()
        
    except Exception as e:
        logger.error(f"Error viewing videos: {e}")

async def test_multiple_videos():
    """Test the multiple videos feature"""
    
    # Check environment
    pexels_key = os.getenv('PEXELS_API_KEY')
    if not pexels_key:
        logger.error("❌ PEXELS_API_KEY not set")
        return
    
    logger.info("🚀 Testing Multiple Videos Feature")
    logger.info("=" * 50)
    
    try:
        # Initialize generator
        generator = EnhancedPinterestGenerator()
        
        # Clear existing data for fresh test
        conn = sqlite3.connect('pinterest_content_enhanced.db')
        cursor = conn.cursor()
        
        # Clear previous test data
        cursor.execute('DELETE FROM video_options')
        cursor.execute('DELETE FROM pinterest_content')
        cursor.execute('DELETE FROM products')
        conn.commit()
        conn.close()
        
        logger.info("🧹 Cleared previous test data")
        
        # Copy product 1 from source
        copied = generator.copy_products_from_source(limit=1)
        logger.info(f"📋 Copied {copied} product(s)")
        
        # Process product 1 with multiple videos
        processed = await generator.process_products_for_pinterest(batch_size=1)
        logger.info(f"⚙️ Processed {processed} product(s)")
        
        # View results
        logger.info("=" * 50)
        logger.info("🎬 MULTIPLE VIDEO RESULTS:")
        view_multiple_videos(1)
        
        logger.info("=" * 50)
        logger.info("✅ Multiple videos test completed!")
        
    except Exception as e:
        logger.error(f"❌ Error in test: {e}")

if __name__ == "__main__":
    asyncio.run(test_multiple_videos())
